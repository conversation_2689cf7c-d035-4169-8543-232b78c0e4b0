<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص الواجهة المحمولة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f8f9fa;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-box {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .status-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .status-error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .status-warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .status-info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        .test-button {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .log-container {
            max-height: 300px;
            overflow-y: auto;
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔍 تشخيص الواجهة المحمولة</h1>
        
        <div class="status-box status-info">
            <h3>📊 معلومات النظام</h3>
            <p><strong>عرض الشاشة:</strong> <span id="screen-width"></span>px</p>
            <p><strong>ارتفاع الشاشة:</strong> <span id="screen-height"></span>px</p>
            <p><strong>نسبة البكسل:</strong> <span id="pixel-ratio"></span></p>
            <p><strong>نوع الجهاز المتوقع:</strong> <span id="device-type"></span></p>
        </div>

        <div class="status-box status-warning">
            <h3>⚠️ حالة التحميل</h3>
            <p><strong>Bootstrap:</strong> <span id="bootstrap-status">جاري التحقق...</span></p>
            <p><strong>Font Awesome:</strong> <span id="fontawesome-status">جاري التحقق...</span></p>
            <p><strong>Leaflet:</strong> <span id="leaflet-status">جاري التحقق...</span></p>
            <p><strong>Device Detector:</strong> <span id="device-detector-status">جاري التحقق...</span></p>
            <p><strong>Mobile App:</strong> <span id="mobile-app-status">جاري التحقق...</span></p>
        </div>

        <div class="status-box status-info">
            <h3>🧪 اختبارات الواجهة</h3>
            <button class="test-button" onclick="testMobileInterface()">اختبار الواجهة المحمولة</button>
            <button class="test-button" onclick="testDeviceDetection()">اختبار كشف الجهاز</button>
            <button class="test-button" onclick="simulateMobileDevice()">محاكاة جهاز محمول</button>
            <button class="test-button" onclick="openMainApp()">فتح التطبيق الرئيسي</button>
        </div>

        <div class="status-box status-success">
            <h3>✅ التحديثات المنجزة</h3>
            <ul>
                <li>✅ حذف الواجهة القديمة للهاتف المحمول من templates/index.html</li>
                <li>✅ حذف مراجع CSS القديمة (mobile.css, mobile-buttons.css, device-specific.css)</li>
                <li>✅ حذف مرجع mobile.js القديم</li>
                <li>✅ إنشاء واجهة جديدة في device-detector.js</li>
                <li>✅ إنشاء فئة MobileApp في mobile-app.js</li>
                <li>✅ دمج CSS الجديد داخل device-detector.js</li>
                <li>✅ ربط الواجهة الجديدة مع APIs الموجودة</li>
            </ul>
        </div>

        <div class="status-box status-info">
            <h3>📝 سجل الأحداث</h3>
            <div class="log-container" id="log-container">
                جاري تحميل السجل...
            </div>
        </div>

        <div class="status-box status-warning">
            <h3>🔧 إرشادات الاختبار</h3>
            <ol>
                <li>افتح أدوات المطور (F12)</li>
                <li>اضغط على أيقونة الهاتف المحمول (Toggle device toolbar)</li>
                <li>اختر جهاز محمول من القائمة (مثل iPhone 12 Pro)</li>
                <li>أعد تحميل الصفحة</li>
                <li>يجب أن تظهر الواجهة الجديدة تلقائياً</li>
            </ol>
        </div>

        <div class="code-block">
كود JavaScript للاختبار:

// فتح وحدة التحكم واكتب:
console.log('Device width:', window.innerWidth);
console.log('Device height:', window.innerHeight);
console.log('User agent:', navigator.userAgent);

// للتحقق من وجود الكائنات:
console.log('Bootstrap:', typeof bootstrap !== 'undefined');
console.log('DeviceDetector:', typeof window.deviceDetector !== 'undefined');
console.log('MobileApp:', typeof window.mobileApp !== 'undefined');
        </div>
    </div>

    <script>
        // تحديث معلومات النظام
        function updateSystemInfo() {
            document.getElementById('screen-width').textContent = window.innerWidth;
            document.getElementById('screen-height').textContent = window.innerHeight;
            document.getElementById('pixel-ratio').textContent = window.devicePixelRatio || 1;
            
            const deviceType = window.innerWidth <= 768 ? 'هاتف محمول 📱' : 
                              window.innerWidth <= 1024 ? 'جهاز لوحي 📱' : 'سطح مكتب 🖥️';
            document.getElementById('device-type').textContent = deviceType;
        }

        // فحص حالة المكتبات
        function checkLibrariesStatus() {
            // Bootstrap
            const bootstrapStatus = typeof bootstrap !== 'undefined' ? '✅ محمل' : '❌ غير محمل';
            document.getElementById('bootstrap-status').textContent = bootstrapStatus;

            // Font Awesome
            const faElements = document.querySelectorAll('[class*="fa-"]');
            const fontAwesomeStatus = faElements.length > 0 ? '✅ محمل' : '❌ غير محمل';
            document.getElementById('fontawesome-status').textContent = fontAwesomeStatus;

            // Leaflet
            const leafletStatus = typeof L !== 'undefined' ? '✅ محمل' : '❌ غير محمل';
            document.getElementById('leaflet-status').textContent = leafletStatus;

            // Device Detector
            const deviceDetectorStatus = typeof window.deviceDetector !== 'undefined' ? '✅ محمل' : '❌ غير محمل';
            document.getElementById('device-detector-status').textContent = deviceDetectorStatus;

            // Mobile App
            const mobileAppStatus = typeof window.mobileApp !== 'undefined' ? '✅ محمل' : '❌ غير محمل';
            document.getElementById('mobile-app-status').textContent = mobileAppStatus;
        }

        // اختبار الواجهة المحمولة
        function testMobileInterface() {
            addLog('🧪 بدء اختبار الواجهة المحمولة...');
            
            if (typeof window.deviceDetector !== 'undefined') {
                addLog('✅ كاشف الأجهزة موجود');
                addLog(`📱 نوع الجهاز: ${window.deviceDetector.deviceType}`);
                addLog(`📏 حجم الشاشة: ${window.deviceDetector.screenWidth}x${window.deviceDetector.screenHeight}`);
            } else {
                addLog('❌ كاشف الأجهزة غير موجود');
            }

            const mobileContainer = document.getElementById('mobile-interface-container');
            if (mobileContainer) {
                addLog('✅ حاوي الواجهة المحمولة موجود');
                addLog(`👁️ حالة العرض: ${mobileContainer.classList.contains('d-none') ? 'مخفي' : 'ظاهر'}`);
            } else {
                addLog('❌ حاوي الواجهة المحمولة غير موجود');
            }
        }

        // اختبار كشف الجهاز
        function testDeviceDetection() {
            addLog('🔍 بدء اختبار كشف الجهاز...');
            addLog(`📱 User Agent: ${navigator.userAgent.substring(0, 100)}...`);
            addLog(`📏 عرض الشاشة: ${window.innerWidth}px`);
            addLog(`📐 ارتفاع الشاشة: ${window.innerHeight}px`);
            addLog(`🔍 نسبة البكسل: ${window.devicePixelRatio}`);
            
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            addLog(`📱 هل هو جهاز محمول؟ ${isMobile ? 'نعم' : 'لا'}`);
        }

        // محاكاة جهاز محمول
        function simulateMobileDevice() {
            addLog('🎭 محاكاة جهاز محمول...');
            
            // تغيير حجم النافذة (لن يعمل في المتصفحات الحديثة لأسباب أمنية)
            try {
                window.resizeTo(375, 667); // iPhone 6/7/8 size
                addLog('📱 تم تغيير حجم النافذة');
            } catch (e) {
                addLog('⚠️ لا يمكن تغيير حجم النافذة - استخدم أدوات المطور');
            }
            
            addLog('💡 للمحاكاة الصحيحة: افتح أدوات المطور (F12) واختر وضع الهاتف المحمول');
        }

        // فتح التطبيق الرئيسي
        function openMainApp() {
            addLog('🚀 فتح التطبيق الرئيسي...');
            window.open('http://localhost:5000', '_blank');
        }

        // إضافة رسالة للسجل
        function addLog(message) {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            logContainer.innerHTML += `[${timestamp}] ${message}\n`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateSystemInfo();
            checkLibrariesStatus();
            addLog('🚀 تم تحميل صفحة التشخيص');
            
            // تحديث المعلومات عند تغيير حجم النافذة
            window.addEventListener('resize', function() {
                updateSystemInfo();
                addLog('📏 تم تغيير حجم النافذة');
            });
            
            // فحص دوري للمكتبات
            setInterval(checkLibrariesStatus, 2000);
        });
    </script>
</body>
</html>
