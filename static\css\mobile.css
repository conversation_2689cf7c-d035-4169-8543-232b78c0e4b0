/**
 * تنسيقات خاصة بواجهة الهاتف المحمول - Loacker
 * تم إنشاؤه: 2024
 */

/* المتغيرات */
:root {
    --loacker-red: #d50000;
    --loacker-red-light: #ff5131;
    --loacker-red-dark: #9b0000;
    --header-height: 60px;
    --footer-height: 60px;
    --content-padding: 15px;
    --card-border-radius: 12px;
    --button-border-radius: 8px;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 6px 16px rgba(0, 0, 0, 0.2);
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* ===== الهيكل الأساسي ===== */
.mobile-view {
    padding: 0;
    background-color: #f8f9fa;
    color: #333;
    font-family: 'Tajawal', sans-serif;
    height: 100vh;
    overflow-x: hidden;
}

/* ===== الرأس ===== */
.mobile-header {
    background-color: white;
    color: #333;
    padding: 0 20px;
    height: var(--header-height);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: var(--shadow-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
}

/* شعار Loacker */
.mobile-logo-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.mobile-logo-wrapper h1 {
    font-size: 1.8rem;
    margin: 0;
    font-weight: 700;
    letter-spacing: -0.5px;
    color: var(--loacker-red);
    font-family: 'Arial', sans-serif;
}

.mobile-logo-wrapper h1::first-letter {
    font-size: 2.2rem;
    font-weight: 900;
    font-family: 'Playfair Display', serif;
    font-style: italic;
    margin-right: 2px;
    vertical-align: -5%;
    line-height: 0.8;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.15);
}

.logo-circle-small-mobile {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    z-index: 1;
    border: 1px solid #f5f5f5;
    margin-left: 10px;
}

.logo-circle-small-mobile i {
    font-size: 1.2rem;
    color: var(--loacker-red);
}

/* زر القائمة */
#mobile-list-toggle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: white;
    color: #555;
    border: 1px solid #eee;
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

#mobile-list-toggle:active {
    transform: scale(0.95);
    background-color: #f8f8f8;
}

/* ===== المحتوى ===== */
.mobile-content {
    margin-top: var(--header-height);
    padding-bottom: calc(var(--footer-height) + 10px);
    min-height: calc(100vh - var(--header-height) - var(--footer-height));
}

/* تبويبات المحتوى */
.mobile-tab-content {
    display: none;
    padding: var(--content-padding);
    animation: fadeIn var(--transition-normal);
}

.mobile-tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ===== الخريطة ===== */
.mobile-map {
    height: 45vh;
    width: 100%;
    border-radius: var(--card-border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    margin-bottom: 15px;
}

/* تنسيقات زر طبقات الخريطة للهاتف */
#mobile-map .leaflet-control-layers-toggle {
    width: 24px !important;
    height: 24px !important;
    border-radius: 2px !important;
    background-color: white !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    margin: 6px !important;
    position: relative !important;
    overflow: hidden !important;
}

/* طبقة الخريطة العادية */
#mobile-map .leaflet-control-layers-toggle::after {
    content: '' !important;
    position: absolute !important;
    top: 5px !important;
    left: 5px !important;
    right: 5px !important;
    height: 5px !important;
    background: #f1f3f4 !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 1px !important;
    z-index: 1 !important;
}

/* طبقة القمر الصناعي */
#mobile-map .leaflet-control-layers-toggle .satellite-layer {
    position: absolute !important;
    top: 13px !important;
    left: 5px !important;
    right: 5px !important;
    height: 5px !important;
    background: #4285F4 !important; /* لون جوجل الأزرق */
    border: 1px solid #3367d6 !important;
    border-radius: 1px !important;
    z-index: 1 !important;
}

/* إضافة ظل للطبقات */
#mobile-map .leaflet-control-layers-toggle::before {
    content: '' !important;
    position: absolute !important;
    top: 3px !important;
    left: 3px !important;
    right: 3px !important;
    bottom: 3px !important;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1) inset !important;
    border-radius: 1px !important;
    z-index: 0 !important;
}

#mobile-map .leaflet-control-layers-expanded {
    padding: 6px !important;
    border-radius: 4px !important;
    min-width: 130px !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
}

#mobile-map .leaflet-control-layers-base label {
    padding: 5px 6px !important;
    font-size: 11px !important;
    margin-bottom: 2px !important;
}

/* ===== قائمة المتاجر ===== */
.mobile-store-list {
    max-height: calc(100vh - var(--header-height) - var(--footer-height) - 120px);
    overflow-y: auto;
    padding: 5px;
}

/* بطاقة المتجر */
.mobile-store-card {
    background-color: white;
    border-radius: var(--card-border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    margin-bottom: 15px;
    border: none;
    transition: all var(--transition-normal);
    border-right: 3px solid transparent;
}

.mobile-store-card:active {
    transform: scale(0.98);
    box-shadow: var(--shadow-md);
}

.mobile-store-card.selected {
    border-right: 3px solid var(--loacker-red);
    background-color: rgba(213, 0, 0, 0.05);
}

.mobile-store-card .card-body {
    padding: 15px;
}

.mobile-store-card .card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.mobile-store-card .card-subtitle {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 10px;
}

/* صورة المتجر */
.mobile-store-image-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.mobile-store-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    object-fit: cover;
    border: 2px solid #f0f0f0;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.mobile-store-image:active {
    transform: scale(1.05);
}

/* أزرار بطاقة المتجر */
.mobile-store-card .btn-group {
    display: flex;
    gap: 8px;
    margin-top: 10px;
}

.mobile-store-card .btn {
    flex: 1;
    font-size: 0.85rem;
    padding: 8px 5px;
    border-radius: var(--button-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    min-width: 40px; /* عرض أدنى للأزرار */
}

.mobile-store-card .btn i {
    font-size: 1rem;
}

.mobile-store-card .btn-outline-primary {
    color: var(--loacker-red);
    border-color: var(--loacker-red);
}

.mobile-store-card .btn-outline-primary:hover,
.mobile-store-card .btn-outline-primary:active {
    background-color: var(--loacker-red);
    border-color: var(--loacker-red);
    color: white;
}

.mobile-store-card .btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545;
}

/* ===== البحث ===== */
.mobile-search {
    margin-bottom: 15px;
    padding: 0 5px;
}

.mobile-search .input-group {
    border-radius: var(--button-border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.mobile-search .form-control {
    border-radius: 0;
    border: 1px solid #eee;
    padding: 10px 15px;
    font-size: 0.95rem;
}

.mobile-search .btn {
    border-radius: 0;
    padding: 0 15px;
}

.mobile-search .btn-outline-primary {
    color: var(--loacker-red);
    border-color: #eee;
}

.mobile-search .btn-outline-primary:hover,
.mobile-search .btn-outline-primary:active {
    background-color: var(--loacker-red);
    border-color: var(--loacker-red);
    color: white;
}

/* ===== النموذج ===== */
.mobile-form {
    padding: 10px 5px;
}

.mobile-form .form-label {
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 5px;
    color: #555;
}

.mobile-form .form-control,
.mobile-form .form-select {
    font-size: 0.95rem;
    padding: 10px 15px;
    border-radius: var(--button-border-radius);
    border: 1px solid #eee;
    background-color: white;
    box-shadow: var(--shadow-sm);
    margin-bottom: 15px;
    transition: all var(--transition-fast);
    appearance: menulist !important;
    -webkit-appearance: menulist !important;
    -moz-appearance: menulist !important;
}

.mobile-form .form-control:focus,
.mobile-form .form-select:focus {
    border-color: var(--loacker-red-light);
    box-shadow: 0 0 0 3px rgba(213, 0, 0, 0.1);
}

.mobile-form .btn {
    padding: 12px;
    font-weight: 500;
    border-radius: var(--button-border-radius);
    transition: all var(--transition-fast);
}

.mobile-form .btn-primary {
    background-color: var(--loacker-red);
    border-color: var(--loacker-red);
}

.mobile-form .btn-primary:hover,
.mobile-form .btn-primary:active {
    background-color: var(--loacker-red-dark);
    border-color: var(--loacker-red-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.mobile-form .btn-primary:active {
    transform: translateY(0);
}

/* معاينة الصورة */
#mobile-image-preview {
    text-align: center;
    margin-bottom: 20px;
}

#mobile-image-preview img {
    max-width: 100%;
    max-height: 200px;
    border-radius: var(--card-border-radius);
    box-shadow: var(--shadow-md);
}

/* ===== شريط التبويبات السفلي ===== */
.mobile-tabs {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: var(--footer-height);
    background-color: white;
    display: flex;
    justify-content: space-around;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    padding: 0;
}

.mobile-tab-btn {
    background-color: transparent;
    color: #777;
    border: none;
    padding: 8px 0;
    font-size: 0.8rem;
    transition: all var(--transition-normal);
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 5px;
    position: relative;
    overflow: hidden;
}

.mobile-tab-btn i {
    font-size: 1.3rem;
    transition: all var(--transition-normal);
}

.mobile-tab-btn:active {
    background-color: rgba(0, 0, 0, 0.05);
    transform: scale(0.95);
}

.mobile-tab-btn.active {
    color: var(--loacker-red);
}

.mobile-tab-btn.active i {
    transform: translateY(-2px);
}

.mobile-tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 25%;
    right: 25%;
    height: 3px;
    background-color: var(--loacker-red);
    border-radius: 3px 3px 0 0;
}

/* ===== زر الإضافة العائم ===== */
.mobile-fab {
    position: fixed;
    bottom: calc(var(--footer-height) + 15px);
    right: 20px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background-color: var(--loacker-red);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    z-index: 990;
    border: none;
    transition: all var(--transition-normal);
}

.mobile-fab i {
    font-size: 24px;
    transition: all var(--transition-normal);
}

.mobile-fab:active {
    transform: scale(0.9);
    box-shadow: var(--shadow-md);
}

.mobile-fab:active i {
    transform: rotate(45deg);
}

/* ===== تحسينات عامة ===== */
/* تحسين شريط التمرير */
.mobile-store-list::-webkit-scrollbar {
    width: 5px;
}

.mobile-store-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.mobile-store-list::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 10px;
}

.mobile-store-list::-webkit-scrollbar-thumb:hover {
    background: #aaa;
}

/* أيقونات الميزات */
.feature-icon {
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    font-size: 1.2rem;
}

/* دائرة الشعار */
.logo-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: var(--loacker-red);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
}

/* تحسين تجربة المستخدم للأزرار */
button, .btn {
    -webkit-tap-highlight-color: transparent;
    position: relative;
    overflow: hidden;
}

/* تأثير الموجة عند الضغط */
button::after, .btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.3);
    opacity: 0;
    border-radius: 100%;
    transform: translate(-50%, -50%) scale(1);
    transform-origin: center;
}

button:active::after, .btn:active::after {
    animation: ripple 0.8s ease-out;
}

@keyframes ripple {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.5;
    }
    100% {
        transform: translate(-50%, -50%) scale(30);
        opacity: 0;
    }
}

/* تحسين عرض العناصر المحددة */
.highlight {
    background-color: rgba(213, 0, 0, 0.1);
    color: var(--loacker-red);
    padding: 0 3px;
    border-radius: 3px;
    font-weight: bold;
}

/* تحسين عرض الشارات */
.badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-weight: 500;
    font-size: 0.85rem;
}

.badge.bg-primary {
    background-color: var(--loacker-red) !important;
}

/* تحسين عرض القوائم المنسدلة */
.dropdown-menu {
    border-radius: var(--card-border-radius);
    box-shadow: var(--shadow-md);
    border: 1px solid #eee;
    padding: 8px 0;
}

.dropdown-item {
    padding: 8px 15px;
    font-size: 0.9rem;
    transition: all var(--transition-fast);
}

.dropdown-item:active {
    background-color: var(--loacker-red);
    color: white;
}

/* إصلاح مشكلة السهم في أزرار القوائم المنسدلة للهاتف المحمول */
.dropdown-toggle-split::after {
    display: inline-block !important;
    margin-left: 0.255em !important;
    vertical-align: 0.255em !important;
    content: "" !important;
    border-top: 0.3em solid !important;
    border-right: 0.3em solid transparent !important;
    border-bottom: 0 !important;
    border-left: 0.3em solid transparent !important;
}

/* تأكيد ظهور السهم في جميع أزرار القوائم المنسدلة */
.dropdown-toggle::after {
    display: inline-block !important;
    margin-left: 0.255em !important;
    vertical-align: 0.255em !important;
    content: "" !important;
    border-top: 0.3em solid !important;
    border-right: 0.3em solid transparent !important;
    border-bottom: 0 !important;
    border-left: 0.3em solid transparent !important;
}

/* تنسيق خاص للأزرار المقسمة في الهاتف المحمول */
.btn-group > .btn:not(:first-child).dropdown-toggle-split::after {
    margin-left: 0 !important;
}

/* تنسيق أيقونة السهم في أزرار القوائم المنسدلة للهاتف المحمول */
.dropdown-toggle-split .fas.fa-chevron-down {
    font-size: 0.75rem !important;
    color: inherit !important;
}

/* إخفاء السهم الافتراضي عند وجود أيقونة Font Awesome */
.dropdown-toggle-split:has(.fas.fa-chevron-down)::after {
    display: none !important;
}

/* تنسيق خاص للأزرار الصغيرة في الهاتف المحمول */
.btn-sm .fas.fa-chevron-down {
    font-size: 0.7rem !important;
}

/* تحسين عرض الإشعارات */
.alert {
    border-radius: var(--card-border-radius);
    box-shadow: var(--shadow-sm);
    padding: 15px;
    margin-bottom: 15px;
    border: none;
}

/* تحسين عرض النوافذ المنبثقة */
.modal-content {
    border-radius: var(--card-border-radius);
    border: none;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
}

/* تأثيرات الموقع الحالي */
.current-location-marker {
    display: flex;
    align-items: center;
    justify-content: center;
}

.pulse-icon {
    color: #4285F4;
    font-size: 1.5rem;
    animation: pulse-animation 2s infinite;
}

@keyframes pulse-animation {
    0% {
        transform: scale(0.8);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(0.8);
        opacity: 1;
    }
}

/* تنسيقات النافذة المنبثقة للخريطة */
.leaflet-popup-content-wrapper {
    border-radius: var(--card-border-radius);
    box-shadow: var(--shadow-md);
    padding: 0;
}

.leaflet-popup-content {
    margin: 0;
    width: 250px !important;
}

.store-popup {
    padding: 15px;
}

.store-popup h5 {
    font-weight: 600;
    color: var(--loacker-red);
}

.store-popup .btn-group {
    margin-top: 10px;
}

.store-popup img {
    border: 1px solid #eee;
    box-shadow: var(--shadow-sm);
}

/* تنسيقات علامات الخريطة */
.store-marker {
    color: var(--loacker-red);
    font-size: 1.8rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.4));
    transition: all 0.3s ease;
}

.selected-marker {
    color: var(--loacker-red-dark);
    font-size: 2.2rem;
    filter: drop-shadow(0 3px 5px rgba(0, 0, 0, 0.5));
}

.store-marker-container:hover .store-marker {
    transform: scale(1.2);
    filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.6));
}

/* تنسيقات دبوس الخريطة */
.fa-map-marker-alt.store-marker {
    animation: marker-bounce 1.5s infinite;
}

@keyframes marker-bounce {
    0%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-8px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* تأثير الظل لدبوس الخريطة */
.store-marker-container {
    position: relative;
}

.store-marker-container::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 8px;
    height: 2px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    filter: blur(1px);
    transition: all 0.3s ease;
}
