<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الواجهة المحمولة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            display: block;
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            background: #007bff;
            color: white;
            text-decoration: none;
            text-align: center;
            border-radius: 5px;
            font-size: 16px;
        }
        .test-button:hover {
            background: #0056b3;
            color: white;
            text-decoration: none;
        }
        .info-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار الواجهة المحمولة</h1>
        
        <div class="info-box">
            <h3>📱 معلومات الاختبار</h3>
            <p>هذه الصفحة لاختبار الواجهة المحمولة الجديدة لبرنامج Loacker</p>
            <p><strong>الهدف:</strong> التأكد من أن الواجهة الجديدة تعمل بشكل صحيح على الأجهزة المحمولة</p>
        </div>

        <div class="success-box">
            <h3>✅ التحديثات المنجزة</h3>
            <ul>
                <li>✅ حذف الواجهة القديمة للهاتف المحمول</li>
                <li>✅ إنشاء واجهة جديدة احترافية</li>
                <li>✅ دمج الواجهة الجديدة مع البرنامج الأساسي</li>
                <li>✅ حذف ملفات CSS القديمة</li>
                <li>✅ تحديث كاشف الأجهزة</li>
                <li>✅ إنشاء فئة MobileApp جديدة</li>
            </ul>
        </div>

        <h3>🔗 روابط الاختبار</h3>
        
        <a href="http://localhost:5000" class="test-button">
            🖥️ فتح البرنامج - واجهة سطح المكتب
        </a>
        
        <a href="http://localhost:5000" class="test-button" onclick="simulateMobile()">
            📱 فتح البرنامج - محاكاة الهاتف المحمول
        </a>

        <div class="info-box">
            <h3>📋 خطوات الاختبار</h3>
            <ol>
                <li>افتح البرنامج على سطح المكتب - يجب أن تظهر الواجهة العادية</li>
                <li>افتح البرنامج على الهاتف المحمول - يجب أن تظهر الواجهة الجديدة</li>
                <li>تأكد من عمل جميع الوظائف (الخريطة، إضافة المتاجر، القوائم)</li>
                <li>تأكد من التصميم المتجاوب</li>
            </ol>
        </div>

        <div class="info-box">
            <h3>🎯 المميزات الجديدة</h3>
            <ul>
                <li><strong>لوحة تحكم:</strong> إحصائيات وإجراءات سريعة</li>
                <li><strong>خرائط محسنة:</strong> خريطة رئيسية وخريطة إضافة منفصلة</li>
                <li><strong>إدارة المتاجر:</strong> بحث متقدم وأزرار تفاعلية</li>
                <li><strong>تصميم احترافي:</strong> ألوان متدرجة وأنيميشن سلس</li>
                <li><strong>تجاوب كامل:</strong> يناسب جميع أحجام الشاشات</li>
            </ul>
        </div>

        <div class="success-box">
            <h3>🎉 النتيجة</h3>
            <p><strong>تم دمج الواجهة الجديدة بنجاح!</strong></p>
            <p>البرنامج الآن يعمل بواجهة موحدة تتكيف تلقائياً مع نوع الجهاز المستخدم.</p>
        </div>
    </div>

    <script>
        function simulateMobile() {
            // محاكاة فتح الرابط في وضع الهاتف المحمول
            alert('💡 نصيحة: لمحاكاة الهاتف المحمول بشكل أفضل:\n\n1. افتح أدوات المطور (F12)\n2. اضغط على أيقونة الهاتف المحمول\n3. اختر جهاز محمول من القائمة\n4. أعد تحميل الصفحة');
        }

        // عرض معلومات الجهاز الحالي
        document.addEventListener('DOMContentLoaded', function() {
            const deviceInfo = document.createElement('div');
            deviceInfo.className = 'info-box';
            deviceInfo.innerHTML = `
                <h3>📊 معلومات الجهاز الحالي</h3>
                <p><strong>عرض الشاشة:</strong> ${window.innerWidth}px</p>
                <p><strong>ارتفاع الشاشة:</strong> ${window.innerHeight}px</p>
                <p><strong>نوع الجهاز المتوقع:</strong> ${window.innerWidth <= 768 ? 'هاتف محمول 📱' : 'سطح مكتب 🖥️'}</p>
                <p><strong>User Agent:</strong> ${navigator.userAgent.substring(0, 100)}...</p>
            `;
            document.querySelector('.test-container').appendChild(deviceInfo);
        });
    </script>
</body>
</html>
