/**
 * DeviceDetector - فئة محسنة للكشف عن نوع الجهاز وحجم الشاشة وتطبيق الواجهة المناسبة
 * تدعم جميع أنواع الهواتف المحمولة بما في ذلك iPhone (7, 8, 11) وأجهزة Xiaomi وغيرها
 * Created: 2024
 */
class DeviceDetector {
    /**
     * إنشاء كائن جديد من كاشف الأجهزة
     * @param {Object} options - خيارات التهيئة
     */
    constructor(options = {}) {
        // الخيارات الافتراضية
        this.options = {
            mobileBreakpoint: 768, // نقطة الفصل بين الأجهزة المحمولة والكمبيوتر (بالبكسل)
            tabletBreakpoint: 1024, // نقطة الفصل بين الأجهزة اللوحية والكمبيوتر (بالبكسل)
            smallPhoneBreakpoint: 375, // نقطة الفصل للهواتف الصغيرة مثل iPhone 7/8
            applyClasses: true, // تطبيق فئات CSS تلقائيًا
            forceDetection: false, // إجبار الكشف عن الأجهزة المحمولة حتى لو كان حجم الشاشة كبيرًا
            ...options
        };

        // حالة الجهاز
        this.isMobile = false;
        this.isTablet = false;
        this.isDesktop = true;
        this.isSmallPhone = false; // للهواتف الصغيرة مثل iPhone 7/8
        this.isLargePhone = false; // للهواتف الكبيرة مثل iPhone 11/Pro Max
        this.deviceType = 'unknown';
        this.deviceModel = 'unknown';
        this.screenWidth = window.innerWidth;
        this.screenHeight = window.innerHeight;
        this.pixelRatio = window.devicePixelRatio || 1;
        this.orientation = this.getOrientation();
        this.userAgent = navigator.userAgent || navigator.vendor || window.opera;

        // الكلمات المفتاحية للأجهزة المحمولة
        this.mobileKeywords = [
            'Android', 'webOS', 'iPhone', 'iPad', 'iPod', 'BlackBerry',
            'IEMobile', 'Opera Mini', 'Mobile', 'mobile', 'Tablet', 'tablet',
            'Mi', 'Redmi', 'POCO', 'Xiaomi', 'SM-', 'Samsung', 'Huawei', 'Honor',
            'OPPO', 'vivo', 'Realme', 'OnePlus', 'Nokia', 'LG', 'HTC', 'Lenovo',
            'ZTE', 'Motorola', 'Sony'
        ];

        // تحديد نوع الجهاز عند الإنشاء
        this.checkDeviceType();

        // إضافة مستمع لتغيير حجم النافذة
        this.setupEventListeners();

        console.log(`🔍 DeviceDetector initialized - Device: ${this.getDeviceTypeString()}, Model: ${this.deviceModel}, Screen: ${this.screenWidth}x${this.screenHeight}, Ratio: ${this.pixelRatio}`);
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // مستمع تغيير حجم النافذة
        window.addEventListener('resize', () => {
            clearTimeout(window.resizeTimer);
            window.resizeTimer = setTimeout(() => {
                this.checkDeviceType();
            }, 250);
        });

        // مستمع تغيير اتجاه الشاشة
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.checkDeviceType();
            }, 300);
        });
    }

    /**
     * التحقق من نوع الجهاز وتحديث الحالة
     */
    checkDeviceType() {
        // تحديث أبعاد الشاشة
        this.screenWidth = window.innerWidth;
        this.screenHeight = window.innerHeight;
        this.pixelRatio = window.devicePixelRatio || 1;
        this.orientation = this.getOrientation();

        // التحقق من وجود كلمات مفتاحية للأجهزة المحمولة
        const hasMobileKeywords = this.mobileKeywords.some(keyword =>
            this.userAgent.indexOf(keyword) !== -1
        );

        // تحديد نوع الجهاز بناءً على حجم الشاشة والكلمات المفتاحية
        const isSmallScreen = this.screenWidth < this.options.mobileBreakpoint;
        const isMediumScreen = this.screenWidth >= this.options.mobileBreakpoint &&
                              this.screenWidth < this.options.tabletBreakpoint;
        const isVerySmallScreen = this.screenWidth <= this.options.smallPhoneBreakpoint;

        // تحديد نوع الجهاز بناءً على المعلومات المتاحة
        this.detectDeviceModel();

        // تحديث حالة الجهاز
        this.isMobile = isSmallScreen || hasMobileKeywords || this.options.forceDetection;
        this.isTablet = !this.isMobile && (isMediumScreen || this.userAgent.indexOf('iPad') !== -1);
        this.isDesktop = !this.isMobile && !this.isTablet;
        this.isSmallPhone = isVerySmallScreen || this.deviceModel.includes('iPhone 7') || this.deviceModel.includes('iPhone 8');
        this.isLargePhone = this.isMobile && !this.isSmallPhone && (this.deviceModel.includes('iPhone 11') || this.deviceModel.includes('iPhone 12') || this.screenWidth >= 390);

        // تطبيق فئات CSS إذا كان مطلوبًا
        if (this.options.applyClasses) {
            this.applyDeviceClasses();
        }

        return {
            isMobile: this.isMobile,
            isTablet: this.isTablet,
            isDesktop: this.isDesktop,
            isSmallPhone: this.isSmallPhone,
            isLargePhone: this.isLargePhone,
            deviceType: this.deviceType,
            deviceModel: this.deviceModel,
            screenWidth: this.screenWidth,
            screenHeight: this.screenHeight,
            pixelRatio: this.pixelRatio,
            orientation: this.orientation
        };
    }

    /**
     * محاولة تحديد طراز الجهاز من سلسلة user agent
     */
    detectDeviceModel() {
        const ua = this.userAgent;

        // تحديد نوع الجهاز الرئيسي
        if (ua.indexOf('iPhone') !== -1) {
            this.deviceType = 'iPhone';

            // محاولة تحديد طراز iPhone بناءً على حجم الشاشة ونسبة البكسل
            if (this.screenWidth === 375 && this.screenHeight === 667 && this.pixelRatio === 2) {
                this.deviceModel = 'iPhone 7/8/SE2';
            } else if (this.screenWidth === 414 && this.screenHeight === 736 && this.pixelRatio === 3) {
                this.deviceModel = 'iPhone 7/8 Plus';
            } else if (this.screenWidth === 375 && this.screenHeight === 812 && this.pixelRatio === 3) {
                this.deviceModel = 'iPhone X/XS/11 Pro';
            } else if (this.screenWidth === 414 && this.screenHeight === 896) {
                this.deviceModel = this.pixelRatio === 2 ? 'iPhone XR/11' : 'iPhone XS Max/11 Pro Max';
            } else if (this.screenWidth === 390 && this.screenHeight === 844 && this.pixelRatio === 3) {
                this.deviceModel = 'iPhone 12/12 Pro/13/13 Pro';
            } else if (this.screenWidth === 428 && this.screenHeight === 926 && this.pixelRatio === 3) {
                this.deviceModel = 'iPhone 12/13 Pro Max';
            } else {
                this.deviceModel = 'iPhone (unknown model)';
            }
        } else if (ua.indexOf('iPad') !== -1) {
            this.deviceType = 'iPad';
            this.deviceModel = 'iPad';
        } else if (ua.indexOf('Xiaomi') !== -1 || ua.indexOf('Mi ') !== -1 || ua.indexOf('Redmi') !== -1) {
            this.deviceType = 'Xiaomi';

            // محاولة استخراج طراز Xiaomi
            const xiaomiRegex = /(Xiaomi|Mi|Redmi|POCO)[\s_-]([^;/]+)/i;
            const match = ua.match(xiaomiRegex);
            if (match) {
                this.deviceModel = `${match[1]} ${match[2]}`;
            } else {
                this.deviceModel = 'Xiaomi (unknown model)';
            }
        } else if (ua.indexOf('Samsung') !== -1 || ua.indexOf('SM-') !== -1) {
            this.deviceType = 'Samsung';

            // محاولة استخراج طراز Samsung
            const samsungRegex = /SM-([^;/\)]+)/i;
            const match = ua.match(samsungRegex);
            if (match) {
                this.deviceModel = `Samsung ${match[1]}`;
            } else {
                this.deviceModel = 'Samsung (unknown model)';
            }
        } else if (ua.indexOf('Android') !== -1) {
            this.deviceType = 'Android';
            this.deviceModel = 'Android Device';
        } else if (ua.indexOf('Windows') !== -1) {
            this.deviceType = 'Windows';
            this.deviceModel = 'Windows Device';
        } else if (ua.indexOf('Macintosh') !== -1) {
            this.deviceType = 'Mac';
            this.deviceModel = 'Mac Device';
        } else if (ua.indexOf('Linux') !== -1) {
            this.deviceType = 'Linux';
            this.deviceModel = 'Linux Device';
        } else {
            this.deviceType = 'Unknown';
            this.deviceModel = 'Unknown Device';
        }
    }

    /**
     * الحصول على اتجاه الشاشة (أفقي أو عمودي)
     * @returns {string} - اتجاه الشاشة ('portrait' أو 'landscape')
     */
    getOrientation() {
        return this.screenWidth > this.screenHeight ? 'landscape' : 'portrait';
    }

    /**
     * الحصول على نوع الجهاز كنص
     * @returns {string} - نوع الجهاز (mobile, tablet, desktop)
     */
    getDeviceTypeString() {
        if (this.isMobile) return 'mobile';
        if (this.isTablet) return 'tablet';
        return 'desktop';
    }

    /**
     * تطبيق فئات CSS المناسبة على عنصر body
     */
    applyDeviceClasses() {
        const body = document.body;
        const html = document.documentElement;

        // إزالة جميع فئات الأجهزة
        body.classList.remove(
            'mobile-device', 'tablet-device', 'desktop-device',
            'small-phone', 'large-phone', 'iphone-device', 'xiaomi-device',
            'samsung-device', 'android-device', 'portrait-mode', 'landscape-mode'
        );

        // إضافة فئة الاتجاه
        body.classList.add(this.orientation === 'portrait' ? 'portrait-mode' : 'landscape-mode');

        // إضافة فئة نوع الجهاز الرئيسي
        if (this.isMobile) {
            body.classList.add('mobile-device');

            // إضافة فئات لنوع الهاتف المحدد
            if (this.isSmallPhone) {
                body.classList.add('small-phone');
            } else if (this.isLargePhone) {
                body.classList.add('large-phone');
            }

            // إضافة فئة لنوع الجهاز
            if (this.deviceType === 'iPhone') {
                body.classList.add('iphone-device');
            } else if (this.deviceType === 'Xiaomi') {
                body.classList.add('xiaomi-device');
            } else if (this.deviceType === 'Samsung') {
                body.classList.add('samsung-device');
            } else if (this.deviceType === 'Android') {
                body.classList.add('android-device');
            }

            // إضافة متغيرات CSS للمساعدة في التنسيق
            html.style.setProperty('--device-width', `${this.screenWidth}px`);
            html.style.setProperty('--device-height', `${this.screenHeight}px`);
            html.style.setProperty('--device-pixel-ratio', this.pixelRatio);
            html.style.setProperty('--device-type', `"${this.deviceType}"`);
            html.style.setProperty('--device-model', `"${this.deviceModel}"`);

            this.applyMobileInterface();
        } else if (this.isTablet) {
            body.classList.add('tablet-device');
            this.applyTabletInterface();
        } else {
            body.classList.add('desktop-device');
            this.applyDesktopInterface();
        }

        // إضافة معلومات الجهاز للمساعدة في تصحيح الأخطاء
        console.log(`📱 Device: ${this.deviceType} (${this.deviceModel}), Screen: ${this.screenWidth}x${this.screenHeight}, Ratio: ${this.pixelRatio}, Mode: ${this.orientation}`);
    }

    /**
     * تطبيق واجهة الهاتف المحمول
     */
    applyMobileInterface() {
        const containerFluid = document.querySelector('.container-fluid');
        const mobileInterfaceContainer = document.getElementById('mobile-interface-container');
        const html = document.documentElement;

        // إخفاء واجهة سطح المكتب وإظهار واجهة الهاتف
        if (containerFluid) {
            containerFluid.style.display = 'none';
        }

        // تحميل الواجهة الجديدة للهاتف المحمول
        if (mobileInterfaceContainer) {
            mobileInterfaceContainer.classList.remove('d-none');
            this.loadMobileInterface(mobileInterfaceContainer);
        }

        // إضافة متغيرات CSS للمساعدة في التنسيق
        html.style.setProperty('--safe-area-inset-top', '0px');
        html.style.setProperty('--safe-area-inset-bottom', '0px');

        // إضافة مناطق آمنة للأجهزة التي تحتوي على notch
        if (this.deviceModel.includes('iPhone X') || this.deviceModel.includes('iPhone 11') || this.deviceModel.includes('iPhone 12') || this.deviceModel.includes('iPhone 13')) {
            html.style.setProperty('--safe-area-inset-top', '44px');
            html.style.setProperty('--safe-area-inset-bottom', '34px');
        }

        // إضافة متغيرات للمساعدة في التنسيق
        if (this.isSmallPhone) {
            html.style.setProperty('--font-size-multiplier', '0.9');
        } else {
            html.style.setProperty('--font-size-multiplier', '1');
        }

        // إضافة متغيرات للمساعدة في التنسيق بناءً على نوع الجهاز
        if (this.deviceType === 'iPhone') {
            html.style.setProperty('--device-font-family', '"SF Pro Text", -apple-system, BlinkMacSystemFont, "Tajawal", sans-serif');
        } else if (this.deviceType === 'Xiaomi') {
            html.style.setProperty('--device-font-family', '"Mi Sans", "Roboto", "Tajawal", sans-serif');
        } else if (this.deviceType === 'Samsung') {
            html.style.setProperty('--device-font-family', '"Samsung Sans", "Roboto", "Tajawal", sans-serif');
        } else {
            html.style.setProperty('--device-font-family', '"Roboto", "Segoe UI", "Tajawal", sans-serif');
        }

        console.log(`📱 تم تطبيق واجهة الهاتف المحمول الجديدة (${this.deviceModel})`);
    }

    /**
     * تحميل واجهة الهاتف المحمول الجديدة
     */
    loadMobileInterface(container) {
        // التحقق من وجود محتوى في الحاوي
        if (container.innerHTML.trim() !== '') {
            console.log('⚠️ الواجهة المحمولة محملة مسبقاً');
            // تهيئة التطبيق فقط إذا لم يكن موجوداً
            if (!window.mobileApp) {
                setTimeout(() => {
                    this.initializeMobileInterface();
                }, 100);
            }
            return;
        }

        // إنشاء الواجهة الجديدة
        const mobileHTML = this.generateMobileHTML();
        container.innerHTML = mobileHTML;

        // تحميل CSS الخاص بالواجهة الجديدة
        this.loadMobileCSS();

        // تهيئة JavaScript للواجهة الجديدة
        setTimeout(() => {
            this.initializeMobileInterface();
        }, 100);
    }

    /**
     * إنشاء HTML للواجهة الجديدة
     */
    generateMobileHTML() {
        return `
            <!-- Header -->
            <header class="mobile-header">
                <div class="logo-container">
                    <div class="logo-icon">
                        <i class="fas fa-store"></i>
                    </div>
                    <div class="logo-text">Loacker</div>
                </div>
                <div class="header-subtitle">نظام إدارة المتاجر المتطور</div>

                <!-- Navigation Tabs -->
                <ul class="nav nav-tabs nav-tabs-mobile" id="mainTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="dashboard-tab" data-bs-toggle="tab" data-bs-target="#dashboard" type="button" role="tab">
                            <i class="fas fa-home"></i> الرئيسية
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="map-tab-mobile" data-bs-toggle="tab" data-bs-target="#map-view" type="button" role="tab">
                            <i class="fas fa-map"></i> الخريطة
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="stores-tab" data-bs-toggle="tab" data-bs-target="#stores-list" type="button" role="tab">
                            <i class="fas fa-list"></i> المتاجر
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="add-tab" data-bs-toggle="tab" data-bs-target="#add-store" type="button" role="tab">
                            <i class="fas fa-plus"></i> إضافة
                        </button>
                    </li>
                </ul>
            </header>

            <!-- Main Content -->
            <div class="content-container">
                <div class="tab-content" id="mainTabsContent">

                    <!-- Dashboard Tab -->
                    <div class="tab-pane fade show active" id="dashboard" role="tabpanel">
                        <!-- User Profile -->
                        <div class="user-profile fade-in">
                            <div class="user-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="user-info">
                                <h6 id="mobile-user-name">مرحباً، مستخدم</h6>
                                <small id="mobile-user-role">مستخدم النظام</small>
                                <span class="status-indicator status-online"></span>
                            </div>
                        </div>

                        <!-- Statistics -->
                        <div class="stats-grid fade-in">
                            <div class="stat-card">
                                <div class="stat-number" id="mobile-total-stores">0</div>
                                <div class="stat-label">إجمالي المتاجر</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="mobile-new-stores">0</div>
                                <div class="stat-label">متاجر جديدة</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="mobile-activity-rate">0%</div>
                                <div class="stat-label">معدل النشاط</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="mobile-pending-stores">0</div>
                                <div class="stat-label">في الانتظار</div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="mobile-card fade-in">
                            <h5 class="mb-3">الإجراءات السريعة</h5>
                            <div class="quick-actions">
                                <div class="quick-action" onclick="mobileApp.switchTab('add-tab')">
                                    <div class="quick-action-icon">
                                        <i class="fas fa-plus-circle"></i>
                                    </div>
                                    <div class="quick-action-label">إضافة متجر</div>
                                </div>
                                <div class="quick-action" onclick="mobileApp.switchTab('map-tab-mobile')">
                                    <div class="quick-action-icon">
                                        <i class="fas fa-map-marked-alt"></i>
                                    </div>
                                    <div class="quick-action-label">عرض الخريطة</div>
                                </div>
                                <div class="quick-action" onclick="mobileApp.exportData()">
                                    <div class="quick-action-icon">
                                        <i class="fas fa-download"></i>
                                    </div>
                                    <div class="quick-action-label">تصدير البيانات</div>
                                </div>
                                <div class="quick-action" onclick="mobileApp.showSettings()">
                                    <div class="quick-action-icon">
                                        <i class="fas fa-cog"></i>
                                    </div>
                                    <div class="quick-action-label">الإعدادات</div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div class="mobile-card fade-in">
                            <h5 class="mb-3">النشاط الأخير</h5>
                            <div class="activity-list" id="mobile-activity-list">
                                <!-- سيتم ملؤها بواسطة JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- Map Tab -->
                    <div class="tab-pane fade" id="map-view" role="tabpanel">
                        <div class="mobile-card fade-in">
                            <h5 class="mb-3">خريطة المتاجر</h5>
                            <div class="map-container">
                                <div id="mobile-map-new"></div>
                            </div>
                            <div class="map-controls mt-3">
                                <button class="btn btn-secondary-mobile btn-sm-mobile me-2" onclick="mobileApp.getCurrentLocation()">
                                    <i class="fas fa-location-arrow"></i> موقعي
                                </button>
                                <button class="btn btn-secondary-mobile btn-sm-mobile me-2" onclick="mobileApp.searchLocation()">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                                <button class="btn btn-secondary-mobile btn-sm-mobile" onclick="mobileApp.filterStores()">
                                    <i class="fas fa-filter"></i> تصفية
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Stores List Tab -->
                    <div class="tab-pane fade" id="stores-list" role="tabpanel">
                        <div class="mobile-card fade-in">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">قائمة المتاجر</h5>
                                <button class="btn btn-primary-mobile btn-sm-mobile" onclick="mobileApp.refreshStores()">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>

                            <!-- Search Bar -->
                            <div class="search-container">
                                <input type="text" class="form-control search-input" placeholder="البحث في المتاجر..." id="mobileStoreSearch">
                                <i class="fas fa-search search-icon"></i>
                            </div>

                            <!-- Stores List -->
                            <div id="mobileStoresList">
                                <!-- سيتم ملؤها بواسطة JavaScript -->
                            </div>

                            <!-- Load More Button -->
                            <div class="text-center mt-3">
                                <button class="btn btn-secondary-mobile" onclick="mobileApp.loadMoreStores()">
                                    <i class="fas fa-plus"></i> تحميل المزيد
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Add Store Tab -->
                    <div class="tab-pane fade" id="add-store" role="tabpanel">
                        <div class="mobile-card fade-in">
                            <h5 class="mb-3">إضافة متجر جديد</h5>

                            <form id="mobileAddStoreForm">
                                <div class="form-group">
                                    <label class="form-label">اسم المتجر *</label>
                                    <input type="text" class="form-control" name="storeName" placeholder="أدخل اسم المتجر" required>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" name="storePhone" placeholder="************">
                                </div>

                                <div class="row">
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label class="form-label">المدينة *</label>
                                            <select class="form-control" name="storeCity" required>
                                                <option value="">اختر المدينة</option>
                                                <option value="طرابلس">طرابلس</option>
                                                <option value="مصراتة">مصراتة</option>
                                                <option value="بنغازي">بنغازي</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label class="form-label">المنطقة</label>
                                            <select class="form-control" name="storeDistrict">
                                                <option value="">اختر المنطقة</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">العنوان الكامل *</label>
                                    <input type="text" class="form-control" name="storeAddress" placeholder="أدخل العنوان الكامل" required>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">وصف المكان</label>
                                    <input type="text" class="form-control" name="storeDescription" placeholder="مثال: بجوار مركز التسوق">
                                </div>

                                <div class="form-group">
                                    <label class="form-label">نوع المتجر</label>
                                    <select class="form-control" name="storeType">
                                        <option value="A">نوع A</option>
                                        <option value="B">نوع B</option>
                                        <option value="C">نوع C</option>
                                        <option value="D">نوع D</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">صورة المتجر</label>
                                    <input type="file" class="form-control" name="storeImage" accept="image/*">
                                    <small class="text-muted">اختر صورة للمتجر (اختياري)</small>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">الموقع على الخريطة</label>
                                    <div class="map-container" style="height: 200px;">
                                        <div id="mobile-add-store-map"></div>
                                    </div>
                                    <small class="text-muted">انقر على الخريطة لتحديد موقع المتجر</small>
                                    <div id="mobile-selected-location" class="mt-2 text-info">لم يتم تحديد موقع</div>
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary-mobile">
                                        <i class="fas fa-plus-circle"></i> إضافة المتجر
                                    </button>
                                    <button type="reset" class="btn btn-secondary-mobile">
                                        <i class="fas fa-times"></i> مسح النموذج
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Floating Action Button -->
            <button class="fab" onclick="mobileApp.switchTab('add-tab')" title="إضافة متجر جديد">
                <i class="fas fa-plus"></i>
            </button>
        `;
    }

    /**
     * تحميل CSS للواجهة الجديدة
     */
    loadMobileCSS() {
        // التحقق من وجود CSS مسبقاً
        if (document.getElementById('mobile-interface-css')) {
            return;
        }

        const css = `
            :root {
                --primary-color: #667eea;
                --secondary-color: #764ba2;
                --accent-color: #f093fb;
                --dark-bg: #1a1a2e;
                --darker-bg: #16213e;
                --card-bg: #0f3460;
                --text-light: #ffffff;
                --text-muted: #b8c6db;
                --success-color: #4ade80;
                --warning-color: #fbbf24;
                --danger-color: #f87171;
                --border-radius: 16px;
                --shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
                --gradient-accent: linear-gradient(135deg, var(--accent-color), var(--primary-color));
            }

            #mobile-interface-container {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100vh;
                z-index: 9999;
                background: var(--dark-bg);
                color: var(--text-light);
                overflow-x: hidden;
                font-family: var(--device-font-family, 'Tajawal', sans-serif);
            }

            #mobile-interface-container::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(45deg, #1a1a2e, #16213e, #0f3460);
                background-size: 400% 400%;
                animation: gradientShift 15s ease infinite;
                z-index: -1;
            }

            @keyframes gradientShift {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }

            .mobile-header {
                background: var(--gradient-primary);
                padding: 1rem;
                position: sticky;
                top: 0;
                z-index: 1000;
                box-shadow: var(--shadow);
                backdrop-filter: blur(10px);
                padding-top: calc(1rem + var(--safe-area-inset-top));
            }

            .logo-container {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.5rem;
                margin-bottom: 0.5rem;
            }

            .logo-icon {
                width: 40px;
                height: 40px;
                background: var(--gradient-accent);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.2rem;
                color: white;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            }

            .logo-text {
                font-size: 1.5rem;
                font-weight: 700;
                color: white;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }

            .header-subtitle {
                text-align: center;
                color: rgba(255, 255, 255, 0.9);
                font-size: 0.9rem;
                font-weight: 400;
            }

            .nav-tabs-mobile {
                background: rgba(255, 255, 255, 0.1);
                border-radius: var(--border-radius);
                padding: 0.25rem;
                margin-top: 1rem;
                backdrop-filter: blur(10px);
                border: none;
            }

            .nav-tabs-mobile .nav-link {
                color: rgba(255, 255, 255, 0.7);
                border: none;
                border-radius: calc(var(--border-radius) - 4px);
                padding: 0.75rem 1rem;
                font-weight: 500;
                transition: all 0.3s ease;
                text-align: center;
                font-size: 0.9rem;
                background: transparent;
            }

            .nav-tabs-mobile .nav-link.active {
                background: var(--gradient-accent);
                color: white;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
                transform: translateY(-2px);
            }

            .nav-tabs-mobile .nav-link:hover:not(.active) {
                background: rgba(255, 255, 255, 0.1);
                color: white;
            }

            .content-container {
                padding: 1rem;
                min-height: calc(100vh - 200px);
                padding-bottom: calc(1rem + var(--safe-area-inset-bottom));
                overflow-y: auto;
            }

            .mobile-card {
                background: var(--card-bg);
                border-radius: var(--border-radius);
                padding: 1.5rem;
                margin-bottom: 1rem;
                box-shadow: var(--shadow);
                border: 1px solid rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                transition: all 0.3s ease;
            }

            .mobile-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
            }

            .form-group {
                margin-bottom: 1.5rem;
            }

            .form-label {
                color: var(--text-light);
                font-weight: 500;
                margin-bottom: 0.5rem;
                display: block;
            }

            .form-control {
                background: rgba(255, 255, 255, 0.1) !important;
                border: 1px solid rgba(255, 255, 255, 0.2) !important;
                border-radius: 12px !important;
                color: var(--text-light) !important;
                padding: 0.75rem 1rem !important;
                font-size: 1rem !important;
                transition: all 0.3s ease !important;
                backdrop-filter: blur(10px) !important;
            }

            .form-control:focus {
                background: rgba(255, 255, 255, 0.15) !important;
                border-color: var(--primary-color) !important;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2) !important;
                color: var(--text-light) !important;
            }

            .form-control::placeholder {
                color: var(--text-muted) !important;
            }

            .btn-mobile {
                padding: 0.75rem 1.5rem;
                border-radius: 12px;
                font-weight: 500;
                font-size: 1rem;
                border: none;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                gap: 0.5rem;
                min-height: 48px;
            }

            .btn-primary-mobile {
                background: var(--gradient-primary) !important;
                color: white !important;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
                border: none !important;
            }

            .btn-primary-mobile:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
                color: white !important;
            }

            .btn-secondary-mobile {
                background: rgba(255, 255, 255, 0.1) !important;
                color: var(--text-light) !important;
                border: 1px solid rgba(255, 255, 255, 0.2) !important;
            }

            .btn-secondary-mobile:hover {
                background: rgba(255, 255, 255, 0.2) !important;
                transform: translateY(-2px);
                color: var(--text-light) !important;
            }

            .btn-success-mobile {
                background: linear-gradient(135deg, var(--success-color), #22c55e) !important;
                color: white !important;
                box-shadow: 0 4px 15px rgba(74, 222, 128, 0.4);
                border: none !important;
            }

            .btn-danger-mobile {
                background: linear-gradient(135deg, var(--danger-color), #ef4444) !important;
                color: white !important;
                box-shadow: 0 4px 15px rgba(248, 113, 113, 0.4);
                border: none !important;
            }

            .btn-sm-mobile {
                padding: 0.5rem 1rem;
                font-size: 0.85rem;
                border-radius: 8px;
            }

            .map-container {
                height: 60vh;
                border-radius: var(--border-radius);
                overflow: hidden;
                box-shadow: var(--shadow);
                border: 1px solid rgba(255, 255, 255, 0.1);
            }

            #mobile-map-new, #mobile-add-store-map {
                height: 100%;
                width: 100%;
            }

            .store-item {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 12px;
                padding: 1rem;
                margin-bottom: 0.75rem;
                border: 1px solid rgba(255, 255, 255, 0.1);
                transition: all 0.3s ease;
            }

            .store-item:hover {
                background: rgba(255, 255, 255, 0.1);
                transform: translateX(5px);
            }

            .store-name {
                font-weight: 600;
                color: var(--text-light);
                margin-bottom: 0.25rem;
            }

            .store-details {
                color: var(--text-muted);
                font-size: 0.9rem;
            }

            .store-actions {
                display: flex;
                gap: 0.5rem;
                margin-top: 0.75rem;
            }

            .user-profile {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                padding: 1rem;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                margin-bottom: 1rem;
            }

            .user-avatar {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background: var(--gradient-accent);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.5rem;
                color: white;
            }

            .user-info h6 {
                margin: 0;
                color: var(--text-light);
                font-weight: 600;
            }

            .user-info small {
                color: var(--text-muted);
            }

            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 1rem;
                margin-bottom: 1.5rem;
            }

            .stat-card {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 12px;
                padding: 1rem;
                text-align: center;
                border: 1px solid rgba(255, 255, 255, 0.1);
            }

            .stat-number {
                font-size: 1.5rem;
                font-weight: 700;
                color: var(--primary-color);
                margin-bottom: 0.25rem;
            }

            .stat-label {
                font-size: 0.85rem;
                color: var(--text-muted);
            }

            .quick-actions {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 1rem;
                margin-bottom: 1.5rem;
            }

            .quick-action {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 12px;
                padding: 1rem;
                text-align: center;
                border: 1px solid rgba(255, 255, 255, 0.1);
                transition: all 0.3s ease;
                cursor: pointer;
            }

            .quick-action:hover {
                background: rgba(255, 255, 255, 0.1);
                transform: translateY(-2px);
            }

            .quick-action-icon {
                font-size: 1.5rem;
                margin-bottom: 0.5rem;
                color: var(--primary-color);
            }

            .quick-action-label {
                font-size: 0.85rem;
                color: var(--text-light);
            }

            .status-indicator {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                display: inline-block;
                margin-left: 0.5rem;
            }

            .status-online { background: var(--success-color); }
            .status-offline { background: var(--danger-color); }
            .status-pending { background: var(--warning-color); }

            .search-container {
                position: relative;
                margin-bottom: 1rem;
            }

            .search-input {
                padding-right: 3rem !important;
            }

            .search-icon {
                position: absolute;
                right: 1rem;
                top: 50%;
                transform: translateY(-50%);
                color: var(--text-muted);
                pointer-events: none;
            }

            .fab {
                position: fixed;
                bottom: 2rem;
                left: 2rem;
                width: 56px;
                height: 56px;
                border-radius: 50%;
                background: var(--gradient-accent);
                color: white;
                border: none;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.5rem;
                transition: all 0.3s ease;
                z-index: 1000;
                bottom: calc(2rem + var(--safe-area-inset-bottom));
            }

            .fab:hover {
                transform: scale(1.1);
                box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
            }

            .fade-in {
                animation: fadeIn 0.5s ease-in;
            }

            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(20px); }
                to { opacity: 1; transform: translateY(0); }
            }

            .activity-list {
                max-height: 300px;
                overflow-y: auto;
            }

            .activity-item {
                display: flex;
                align-items: center;
                gap: 1rem;
                padding: 0.75rem;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                transition: all 0.3s ease;
            }

            .activity-item:hover {
                background: rgba(255, 255, 255, 0.05);
            }

            .activity-item:last-child {
                border-bottom: none;
            }

            .activity-icon {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.1);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1rem;
            }

            .activity-content {
                flex: 1;
            }

            .activity-title {
                color: var(--text-light);
                font-weight: 500;
                margin-bottom: 0.25rem;
            }

            .activity-time {
                color: var(--text-muted);
                font-size: 0.85rem;
            }

            @media (max-width: 576px) {
                .content-container {
                    padding: 0.75rem;
                }

                .mobile-card {
                    padding: 1rem;
                }

                .nav-tabs-mobile .nav-link {
                    padding: 0.5rem 0.75rem;
                    font-size: 0.8rem;
                }
            }
        `;

        const style = document.createElement('style');
        style.id = 'mobile-interface-css';
        style.textContent = css;
        document.head.appendChild(style);
    }

    /**
     * تهيئة JavaScript للواجهة الجديدة
     */
    initializeMobileInterface() {
        // التحقق من وجود التطبيق المحمول مسبقاً
        if (window.mobileApp) {
            console.log('⚠️ التطبيق المحمول مهيئ مسبقاً، سيتم إعادة استخدامه');
            return;
        }

        // إنشاء كائن التطبيق المحمول
        try {
            window.mobileApp = new MobileApp();
            console.log('📱 تم تهيئة التطبيق المحمول بنجاح');
        } catch (error) {
            console.error('❌ خطأ في تهيئة التطبيق المحمول:', error);
        }
    }

    /**
     * تطبيق واجهة الجهاز اللوحي
     */
    applyTabletInterface() {
        const containerFluid = document.querySelector('.container-fluid');
        const mobileView = document.querySelector('.mobile-view');

        // يمكن تخصيص سلوك الأجهزة اللوحية هنا
        // في هذه الحالة نستخدم واجهة سطح المكتب للأجهزة اللوحية
        if (containerFluid) containerFluid.style.display = 'block';
        if (mobileView) mobileView.style.display = 'none';

        console.log('📱 تم تطبيق واجهة الجهاز اللوحي');
    }

    /**
     * تطبيق واجهة سطح المكتب
     */
    applyDesktopInterface() {
        const containerFluid = document.querySelector('.container-fluid');
        const mobileView = document.querySelector('.mobile-view');

        if (containerFluid) containerFluid.style.display = 'block';
        if (mobileView) mobileView.style.display = 'none';

        console.log('🖥️ تم تطبيق واجهة سطح المكتب');
    }
}

// إنشاء كائن عام من كاشف الأجهزة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // إنشاء كائن DeviceDetector وجعله متاحًا عالميًا
    window.deviceDetector = new DeviceDetector();
});
