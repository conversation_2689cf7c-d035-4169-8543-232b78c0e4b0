<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#1a1a2e">
    <title>Loacker - واجهة الهاتف المحمول</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts - Tajawal -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Leaflet CSS for Maps -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --dark-bg: #1a1a2e;
            --darker-bg: #16213e;
            --card-bg: #0f3460;
            --text-light: #ffffff;
            --text-muted: #b8c6db;
            --success-color: #4ade80;
            --warning-color: #fbbf24;
            --danger-color: #f87171;
            --border-radius: 16px;
            --shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            --gradient-accent: linear-gradient(135deg, var(--accent-color), var(--primary-color));
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            background: var(--dark-bg);
            color: var(--text-light);
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #1a1a2e, #16213e, #0f3460);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            z-index: -1;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Header Styles */
        .mobile-header {
            background: var(--gradient-primary);
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow);
            backdrop-filter: blur(10px);
        }

        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: var(--gradient-accent);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .logo-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header-subtitle {
            text-align: center;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
            font-weight: 400;
        }

        /* Navigation Tabs */
        .nav-tabs-mobile {
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius);
            padding: 0.25rem;
            margin-top: 1rem;
            backdrop-filter: blur(10px);
        }

        .nav-tabs-mobile .nav-link {
            color: rgba(255, 255, 255, 0.7);
            border: none;
            border-radius: calc(var(--border-radius) - 4px);
            padding: 0.75rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 0.9rem;
        }

        .nav-tabs-mobile .nav-link.active {
            background: var(--gradient-accent);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transform: translateY(-2px);
        }

        .nav-tabs-mobile .nav-link:hover:not(.active) {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        /* Content Container */
        .content-container {
            padding: 1rem;
            min-height: calc(100vh - 200px);
        }

        /* Card Styles */
        .mobile-card {
            background: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: var(--shadow);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .mobile-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            color: var(--text-light);
            font-weight: 500;
            margin-bottom: 0.5rem;
            display: block;
        }

        .form-control {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: var(--text-light);
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-control:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
            color: var(--text-light);
        }

        .form-control::placeholder {
            color: var(--text-muted);
        }

        /* Button Styles */
        .btn-mobile {
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 500;
            font-size: 1rem;
            border: none;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            min-height: 48px;
        }

        .btn-primary-mobile {
            background: var(--gradient-primary);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-primary-mobile:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .btn-secondary-mobile {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-light);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary-mobile:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .btn-success-mobile {
            background: linear-gradient(135deg, var(--success-color), #22c55e);
            color: white;
            box-shadow: 0 4px 15px rgba(74, 222, 128, 0.4);
        }

        .btn-danger-mobile {
            background: linear-gradient(135deg, var(--danger-color), #ef4444);
            color: white;
            box-shadow: 0 4px 15px rgba(248, 113, 113, 0.4);
        }

        /* Map Container */
        .map-container {
            height: 60vh;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        #mobile-map {
            height: 100%;
            width: 100%;
        }

        /* Store List Styles */
        .store-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .store-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .store-name {
            font-weight: 600;
            color: var(--text-light);
            margin-bottom: 0.25rem;
        }

        .store-details {
            color: var(--text-muted);
            font-size: 0.9rem;
        }

        .store-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.75rem;
        }

        .btn-sm-mobile {
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
            border-radius: 8px;
        }

        /* Loading Animation */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 576px) {
            .content-container {
                padding: 0.75rem;
            }

            .mobile-card {
                padding: 1rem;
            }

            .nav-tabs-mobile .nav-link {
                padding: 0.5rem 0.75rem;
                font-size: 0.8rem;
            }
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in-right {
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* Status Indicators */
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 0.5rem;
        }

        .status-online { background: var(--success-color); }
        .status-offline { background: var(--danger-color); }
        .status-pending { background: var(--warning-color); }

        /* Search Bar */
        .search-container {
            position: relative;
            margin-bottom: 1rem;
        }

        .search-input {
            padding-right: 3rem;
        }

        .search-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
        }

        /* Floating Action Button */
        .fab {
            position: fixed;
            bottom: 2rem;
            left: 2rem;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: var(--gradient-accent);
            color: white;
            border: none;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
        }

        /* User Profile Section */
        .user-profile {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            margin-bottom: 1rem;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--gradient-accent);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .user-info h6 {
            margin: 0;
            color: var(--text-light);
            font-weight: 600;
        }

        .user-info small {
            color: var(--text-muted);
        }

        /* Statistics Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.85rem;
            color: var(--text-muted);
        }

        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .quick-action {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .quick-action:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .quick-action-icon {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }

        .quick-action-label {
            font-size: 0.85rem;
            color: var(--text-light);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="mobile-header">
        <div class="logo-container">
            <div class="logo-icon">
                <i class="fas fa-store"></i>
            </div>
            <div class="logo-text">Loacker</div>
        </div>
        <div class="header-subtitle">نظام إدارة المتاجر المتطور</div>

        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs nav-tabs-mobile" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="dashboard-tab" data-bs-toggle="tab" data-bs-target="#dashboard" type="button" role="tab">
                    <i class="fas fa-home"></i> الرئيسية
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="map-tab" data-bs-toggle="tab" data-bs-target="#map-view" type="button" role="tab">
                    <i class="fas fa-map"></i> الخريطة
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="stores-tab" data-bs-toggle="tab" data-bs-target="#stores-list" type="button" role="tab">
                    <i class="fas fa-list"></i> المتاجر
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="add-tab" data-bs-toggle="tab" data-bs-target="#add-store" type="button" role="tab">
                    <i class="fas fa-plus"></i> إضافة
                </button>
            </li>
        </ul>
    </header>

    <!-- Main Content -->
    <div class="content-container">
        <div class="tab-content" id="mainTabsContent">

            <!-- Dashboard Tab -->
            <div class="tab-pane fade show active" id="dashboard" role="tabpanel">
                <!-- User Profile -->
                <div class="user-profile fade-in">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-info">
                        <h6>مرحباً، أحمد محمد</h6>
                        <small>مدير النظام</small>
                        <span class="status-indicator status-online"></span>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="stats-grid fade-in">
                    <div class="stat-card">
                        <div class="stat-number">127</div>
                        <div class="stat-label">إجمالي المتاجر</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">23</div>
                        <div class="stat-label">متاجر جديدة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">95%</div>
                        <div class="stat-label">معدل النشاط</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">12</div>
                        <div class="stat-label">في الانتظار</div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="mobile-card fade-in">
                    <h5 class="mb-3">الإجراءات السريعة</h5>
                    <div class="quick-actions">
                        <div class="quick-action" onclick="switchTab('add-tab')">
                            <div class="quick-action-icon">
                                <i class="fas fa-plus-circle"></i>
                            </div>
                            <div class="quick-action-label">إضافة متجر</div>
                        </div>
                        <div class="quick-action" onclick="switchTab('map-tab')">
                            <div class="quick-action-icon">
                                <i class="fas fa-map-marked-alt"></i>
                            </div>
                            <div class="quick-action-label">عرض الخريطة</div>
                        </div>
                        <div class="quick-action" onclick="exportData()">
                            <div class="quick-action-icon">
                                <i class="fas fa-download"></i>
                            </div>
                            <div class="quick-action-label">تصدير البيانات</div>
                        </div>
                        <div class="quick-action" onclick="showSettings()">
                            <div class="quick-action-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="quick-action-label">الإعدادات</div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="mobile-card fade-in">
                    <h5 class="mb-3">النشاط الأخير</h5>
                    <div class="activity-list">
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-plus text-success"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">تم إضافة متجر جديد</div>
                                <div class="activity-time">منذ 5 دقائق</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-edit text-warning"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">تم تحديث بيانات متجر</div>
                                <div class="activity-time">منذ 15 دقيقة</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-trash text-danger"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">تم حذف متجر</div>
                                <div class="activity-time">منذ ساعة</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Map Tab -->
            <div class="tab-pane fade" id="map-view" role="tabpanel">
                <div class="mobile-card fade-in">
                    <h5 class="mb-3">خريطة المتاجر</h5>
                    <div class="map-container">
                        <div id="mobile-map"></div>
                    </div>
                    <div class="map-controls mt-3">
                        <button class="btn btn-secondary-mobile btn-sm-mobile me-2">
                            <i class="fas fa-location-arrow"></i> موقعي
                        </button>
                        <button class="btn btn-secondary-mobile btn-sm-mobile me-2">
                            <i class="fas fa-search"></i> بحث
                        </button>
                        <button class="btn btn-secondary-mobile btn-sm-mobile">
                            <i class="fas fa-filter"></i> تصفية
                        </button>
                    </div>
                </div>
            </div>

            <!-- Stores List Tab -->
            <div class="tab-pane fade" id="stores-list" role="tabpanel">
                <div class="mobile-card fade-in">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">قائمة المتاجر</h5>
                        <button class="btn btn-primary-mobile btn-sm-mobile" onclick="refreshStores()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>

                    <!-- Search Bar -->
                    <div class="search-container">
                        <input type="text" class="form-control search-input" placeholder="البحث في المتاجر..." id="storeSearch">
                        <i class="fas fa-search search-icon"></i>
                    </div>

                    <!-- Stores List -->
                    <div id="storesList">
                        <div class="store-item">
                            <div class="store-name">متجر الأمل للإلكترونيات</div>
                            <div class="store-details">
                                <i class="fas fa-map-marker-alt"></i> طرابلس - الظهرة
                                <br>
                                <i class="fas fa-phone"></i> ************
                                <span class="status-indicator status-online"></span>
                            </div>
                            <div class="store-actions">
                                <button class="btn btn-primary-mobile btn-sm-mobile">
                                    <i class="fas fa-eye"></i> عرض
                                </button>
                                <button class="btn btn-secondary-mobile btn-sm-mobile">
                                    <i class="fas fa-edit"></i> تعديل
                                </button>
                                <button class="btn btn-danger-mobile btn-sm-mobile">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>

                        <div class="store-item">
                            <div class="store-name">سوبر ماركت النور</div>
                            <div class="store-details">
                                <i class="fas fa-map-marker-alt"></i> مصراتة - وسط المدينة
                                <br>
                                <i class="fas fa-phone"></i> ************
                                <span class="status-indicator status-pending"></span>
                            </div>
                            <div class="store-actions">
                                <button class="btn btn-primary-mobile btn-sm-mobile">
                                    <i class="fas fa-eye"></i> عرض
                                </button>
                                <button class="btn btn-secondary-mobile btn-sm-mobile">
                                    <i class="fas fa-edit"></i> تعديل
                                </button>
                                <button class="btn btn-danger-mobile btn-sm-mobile">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>

                        <div class="store-item">
                            <div class="store-name">مطعم البحر الأبيض</div>
                            <div class="store-details">
                                <i class="fas fa-map-marker-alt"></i> بنغازي - الصابري
                                <br>
                                <i class="fas fa-phone"></i> ************
                                <span class="status-indicator status-offline"></span>
                            </div>
                            <div class="store-actions">
                                <button class="btn btn-primary-mobile btn-sm-mobile">
                                    <i class="fas fa-eye"></i> عرض
                                </button>
                                <button class="btn btn-secondary-mobile btn-sm-mobile">
                                    <i class="fas fa-edit"></i> تعديل
                                </button>
                                <button class="btn btn-danger-mobile btn-sm-mobile">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Load More Button -->
                    <div class="text-center mt-3">
                        <button class="btn btn-secondary-mobile" onclick="loadMoreStores()">
                            <i class="fas fa-plus"></i> تحميل المزيد
                        </button>
                    </div>
                </div>
            </div>

            <!-- Add Store Tab -->
            <div class="tab-pane fade" id="add-store" role="tabpanel">
                <div class="mobile-card fade-in">
                    <h5 class="mb-3">إضافة متجر جديد</h5>

                    <form id="addStoreForm">
                        <div class="form-group">
                            <label class="form-label">اسم المتجر *</label>
                            <input type="text" class="form-control" placeholder="أدخل اسم المتجر" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" placeholder="************">
                        </div>

                        <div class="row">
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label">المدينة *</label>
                                    <select class="form-control" required>
                                        <option value="">اختر المدينة</option>
                                        <option value="tripoli">طرابلس</option>
                                        <option value="misrata">مصراتة</option>
                                        <option value="benghazi">بنغازي</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label">المنطقة</label>
                                    <select class="form-control">
                                        <option value="">اختر المنطقة</option>
                                        <option value="center">وسط المدينة</option>
                                        <option value="dahra">الظهرة</option>
                                        <option value="sabri">الصابري</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">العنوان الكامل *</label>
                            <input type="text" class="form-control" placeholder="أدخل العنوان الكامل" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">وصف المكان</label>
                            <input type="text" class="form-control" placeholder="مثال: بجوار مركز التسوق">
                        </div>

                        <div class="form-group">
                            <label class="form-label">نوع المتجر</label>
                            <select class="form-control">
                                <option value="A">نوع A</option>
                                <option value="B">نوع B</option>
                                <option value="C">نوع C</option>
                                <option value="D">نوع D</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">صورة المتجر</label>
                            <input type="file" class="form-control" accept="image/*">
                            <small class="text-muted">اختر صورة للمتجر (اختياري)</small>
                        </div>

                        <div class="form-group">
                            <label class="form-label">الموقع على الخريطة</label>
                            <div class="map-container" style="height: 200px;">
                                <div id="add-store-map"></div>
                            </div>
                            <small class="text-muted">انقر على الخريطة لتحديد موقع المتجر</small>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary-mobile">
                                <i class="fas fa-plus-circle"></i> إضافة المتجر
                            </button>
                            <button type="reset" class="btn btn-secondary-mobile">
                                <i class="fas fa-times"></i> مسح النموذج
                            </button>
                        </div>
                    </form>
                </div>
            </div>

        </div>
    </div>

    <!-- Floating Action Button -->
    <button class="fab" onclick="switchTab('add-tab')" title="إضافة متجر جديد">
        <i class="fas fa-plus"></i>
    </button>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

    <script>
        // Global variables
        let map, addStoreMap;
        let stores = [];
        let currentLocation = null;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeMaps();
            loadStores();
            setupEventListeners();
            addAnimations();
        });

        // Initialize maps
        function initializeMaps() {
            // Main map
            if (document.getElementById('mobile-map')) {
                map = L.map('mobile-map').setView([32.8872, 13.1913], 6); // Libya center
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors'
                }).addTo(map);
            }

            // Add store map
            if (document.getElementById('add-store-map')) {
                addStoreMap = L.map('add-store-map').setView([32.8872, 13.1913], 6);
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors'
                }).addTo(addStoreMap);

                // Add click event to select location
                addStoreMap.on('click', function(e) {
                    if (window.selectedMarker) {
                        addStoreMap.removeLayer(window.selectedMarker);
                    }
                    window.selectedMarker = L.marker([e.latlng.lat, e.latlng.lng]).addTo(addStoreMap);
                    window.selectedLocation = e.latlng;
                    showNotification('تم تحديد الموقع بنجاح', 'success');
                });
            }
        }

        // Load stores data
        function loadStores() {
            // Simulate loading stores
            stores = [
                {
                    id: 1,
                    name: 'متجر الأمل للإلكترونيات',
                    phone: '************',
                    city: 'طرابلس',
                    district: 'الظهرة',
                    lat: 32.8872,
                    lng: 13.1913,
                    status: 'online'
                },
                {
                    id: 2,
                    name: 'سوبر ماركت النور',
                    phone: '************',
                    city: 'مصراتة',
                    district: 'وسط المدينة',
                    lat: 32.3745,
                    lng: 15.0919,
                    status: 'pending'
                },
                {
                    id: 3,
                    name: 'مطعم البحر الأبيض',
                    phone: '************',
                    city: 'بنغازي',
                    district: 'الصابري',
                    lat: 32.1165,
                    lng: 20.0686,
                    status: 'offline'
                }
            ];

            // Add markers to map
            if (map) {
                stores.forEach(store => {
                    const marker = L.marker([store.lat, store.lng]).addTo(map);
                    marker.bindPopup(`
                        <div class="store-popup">
                            <h6>${store.name}</h6>
                            <p><i class="fas fa-map-marker-alt"></i> ${store.city} - ${store.district}</p>
                            <p><i class="fas fa-phone"></i> ${store.phone}</p>
                            <div class="popup-actions">
                                <button class="btn btn-primary-mobile btn-sm-mobile" onclick="viewStore(${store.id})">
                                    <i class="fas fa-eye"></i> عرض
                                </button>
                            </div>
                        </div>
                    `);
                });
            }
        }

        // Setup event listeners
        function setupEventListeners() {
            // Search functionality
            const searchInput = document.getElementById('storeSearch');
            if (searchInput) {
                searchInput.addEventListener('input', function(e) {
                    filterStores(e.target.value);
                });
            }

            // Form submission
            const addStoreForm = document.getElementById('addStoreForm');
            if (addStoreForm) {
                addStoreForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    addNewStore();
                });
            }

            // Tab change events
            const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
            tabButtons.forEach(button => {
                button.addEventListener('shown.bs.tab', function(e) {
                    const targetId = e.target.getAttribute('data-bs-target');
                    if (targetId === '#map-view' && map) {
                        setTimeout(() => map.invalidateSize(), 100);
                    }
                    if (targetId === '#add-store' && addStoreMap) {
                        setTimeout(() => addStoreMap.invalidateSize(), 100);
                    }
                });
            });
        }

        // Add animations
        function addAnimations() {
            // Animate cards on scroll
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('fade-in');
                    }
                });
            });

            document.querySelectorAll('.mobile-card, .store-item').forEach(el => {
                observer.observe(el);
            });
        }

        // Utility functions
        function switchTab(tabId) {
            const tab = document.getElementById(tabId);
            if (tab) {
                const tabInstance = new bootstrap.Tab(tab);
                tabInstance.show();
            }
        }

        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto remove after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }

        function refreshStores() {
            showNotification('جاري تحديث قائمة المتاجر...', 'info');
            // Simulate refresh
            setTimeout(() => {
                showNotification('تم تحديث قائمة المتاجر بنجاح', 'success');
            }, 1000);
        }

        function loadMoreStores() {
            showNotification('جاري تحميل المزيد من المتاجر...', 'info');
            // Simulate loading more stores
            setTimeout(() => {
                showNotification('تم تحميل المزيد من المتاجر', 'success');
            }, 1000);
        }

        function filterStores(searchTerm) {
            const storeItems = document.querySelectorAll('.store-item');
            storeItems.forEach(item => {
                const storeName = item.querySelector('.store-name').textContent;
                const storeDetails = item.querySelector('.store-details').textContent;

                if (storeName.includes(searchTerm) || storeDetails.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function addNewStore() {
            if (!window.selectedLocation) {
                showNotification('يرجى تحديد موقع المتجر على الخريطة', 'warning');
                return;
            }

            showNotification('جاري إضافة المتجر...', 'info');

            // Simulate adding store
            setTimeout(() => {
                showNotification('تم إضافة المتجر بنجاح', 'success');
                document.getElementById('addStoreForm').reset();
                if (window.selectedMarker) {
                    addStoreMap.removeLayer(window.selectedMarker);
                    window.selectedMarker = null;
                    window.selectedLocation = null;
                }
            }, 1000);
        }

        function viewStore(storeId) {
            showNotification(`عرض تفاصيل المتجر رقم ${storeId}`, 'info');
        }

        function exportData() {
            showNotification('جاري تصدير البيانات...', 'info');
            setTimeout(() => {
                showNotification('تم تصدير البيانات بنجاح', 'success');
            }, 1000);
        }

        function showSettings() {
            showNotification('فتح الإعدادات...', 'info');
        }

        // Get current location
        function getCurrentLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        currentLocation = {
                            lat: position.coords.latitude,
                            lng: position.coords.longitude
                        };

                        if (map) {
                            map.setView([currentLocation.lat, currentLocation.lng], 15);
                            L.marker([currentLocation.lat, currentLocation.lng])
                                .addTo(map)
                                .bindPopup('موقعك الحالي')
                                .openPopup();
                        }

                        showNotification('تم تحديد موقعك الحالي', 'success');
                    },
                    function(error) {
                        showNotification('لا يمكن تحديد موقعك الحالي', 'error');
                    }
                );
            } else {
                showNotification('المتصفح لا يدعم تحديد الموقع', 'error');
            }
        }

        // Add some CSS for activity items
        const activityCSS = `
            .activity-list {
                max-height: 300px;
                overflow-y: auto;
            }

            .activity-item {
                display: flex;
                align-items: center;
                gap: 1rem;
                padding: 0.75rem;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                transition: all 0.3s ease;
            }

            .activity-item:hover {
                background: rgba(255, 255, 255, 0.05);
            }

            .activity-item:last-child {
                border-bottom: none;
            }

            .activity-icon {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.1);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1rem;
            }

            .activity-content {
                flex: 1;
            }

            .activity-title {
                color: var(--text-light);
                font-weight: 500;
                margin-bottom: 0.25rem;
            }

            .activity-time {
                color: var(--text-muted);
                font-size: 0.85rem;
            }

            .store-popup {
                text-align: center;
                min-width: 200px;
            }

            .store-popup h6 {
                margin-bottom: 0.5rem;
                color: #333;
            }

            .store-popup p {
                margin-bottom: 0.25rem;
                color: #666;
                font-size: 0.9rem;
            }

            .popup-actions {
                margin-top: 0.75rem;
            }
        `;

        // Add the CSS to the document
        const style = document.createElement('style');
        style.textContent = activityCSS;
        document.head.appendChild(style);
    </script>
</body>
</html>
