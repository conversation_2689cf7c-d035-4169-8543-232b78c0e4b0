/**
 * MobileApp - فئة التطبيق المحمول الجديد
 * تدير جميع وظائف الواجهة المحمولة الجديدة
 */
class MobileApp {
    constructor() {
        this.map = null;
        this.addStoreMap = null;
        this.stores = [];
        this.currentLocation = null;
        this.selectedLocation = null;
        this.selectedMarker = null;
        this.currentUser = null;
        this.statistics = {
            totalStores: 0,
            newStores: 0,
            activityRate: 0,
            pendingStores: 0
        };

        this.init();
    }

    /**
     * تهيئة التطبيق
     */
    init() {
        console.log('🚀 تهيئة التطبيق المحمول...');

        // تحميل بيانات المستخدم
        setTimeout(() => {
            this.loadUserData();
        }, 300);

        // تحميل البيانات
        setTimeout(() => {
            this.loadData();
        }, 600);

        // إعداد مستمعات الأحداث
        setTimeout(() => {
            this.setupEventListeners();
        }, 900);

        // تهيئة الخرائط
        setTimeout(() => {
            this.initializeMaps();
        }, 1200);

        // إضافة الأنيميشن
        setTimeout(() => {
            this.addAnimations();
        }, 1500);

        console.log('✅ تم تهيئة التطبيق المحمول بنجاح');
    }

    /**
     * تحميل بيانات المستخدم
     */
    loadUserData() {
        try {
            // محاولة الحصول على بيانات المستخدم من العناصر الموجودة
            const userNameElement = document.querySelector('.user-info h6');
            const userRoleElement = document.querySelector('.user-info small');

            const mobileUserNameElement = document.getElementById('mobile-user-name');
            const mobileUserRoleElement = document.getElementById('mobile-user-role');

            if (userNameElement && userNameElement.textContent.includes('مرحباً') && mobileUserNameElement) {
                const userName = userNameElement.textContent.replace('مرحباً، ', '');
                mobileUserNameElement.textContent = `مرحباً، ${userName}`;
            } else if (mobileUserNameElement) {
                mobileUserNameElement.textContent = 'مرحباً، مستخدم';
            }

            if (userRoleElement && mobileUserRoleElement) {
                mobileUserRoleElement.textContent = userRoleElement.textContent;
            } else if (mobileUserRoleElement) {
                mobileUserRoleElement.textContent = 'مستخدم النظام';
            }

            // تحديث بيانات المستخدم من الخادم
            this.fetchUserData();
        } catch (error) {
            console.error('❌ خطأ في تحميل بيانات المستخدم:', error);
        }
    }

    /**
     * جلب بيانات المستخدم من الخادم
     */
    async fetchUserData() {
        try {
            // يمكن إضافة API endpoint هنا لجلب بيانات المستخدم
            console.log('📡 جاري تحميل بيانات المستخدم...');
        } catch (error) {
            console.error('❌ خطأ في تحميل بيانات المستخدم:', error);
        }
    }

    /**
     * تهيئة الخرائط
     */
    initializeMaps() {
        console.log('🗺️ تهيئة الخرائط...');

        // تنظيف الخرائط الموجودة
        this.cleanupExistingMaps();

        // تهيئة الخريطة الرئيسية
        this.initMainMap();

        // تهيئة خريطة إضافة المتجر
        this.initAddStoreMap();
    }

    /**
     * تنظيف الخرائط الموجودة
     */
    cleanupExistingMaps() {
        // تنظيف الخريطة الرئيسية
        if (this.map) {
            try {
                this.map.remove();
                this.map = null;
                console.log('🧹 تم تنظيف الخريطة الرئيسية');
            } catch (error) {
                console.warn('⚠️ خطأ في تنظيف الخريطة الرئيسية:', error);
                this.map = null;
            }
        }

        // تنظيف خريطة إضافة المتجر
        if (this.addStoreMap) {
            try {
                this.addStoreMap.remove();
                this.addStoreMap = null;
                console.log('🧹 تم تنظيف خريطة إضافة المتجر');
            } catch (error) {
                console.warn('⚠️ خطأ في تنظيف خريطة إضافة المتجر:', error);
                this.addStoreMap = null;
            }
        }

        // تنظيف عناصر الخرائط
        this.resetMapElement('mobile-map-new');
        this.resetMapElement('mobile-add-store-map');
    }

    /**
     * تهيئة الخريطة الرئيسية
     */
    initMainMap() {
        // انتظار تحميل DOM
        setTimeout(() => {
            const mapElement = document.getElementById('mobile-map-new');
            if (!mapElement) {
                console.warn('⚠️ عنصر الخريطة الرئيسية غير موجود');
                return;
            }

            if (typeof L === 'undefined') {
                console.warn('⚠️ مكتبة Leaflet غير محملة');
                return;
            }

            // التحقق من وجود خريطة مهيئة مسبقاً
            if (this.map) {
                console.log('⚠️ الخريطة الرئيسية مهيئة مسبقاً، سيتم إعادة استخدامها');
                return;
            }

            // التحقق من وجود خريطة في العنصر
            if (mapElement._leaflet_id) {
                console.log('⚠️ عنصر الخريطة مستخدم مسبقاً، سيتم إعادة تعيينه');
                mapElement._leaflet_id = null;
                mapElement.innerHTML = '';
            }

            try {
                this.map = L.map('mobile-map-new').setView([32.8872, 13.1913], 6);

                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors'
                }).addTo(this.map);

                console.log('✅ تم تهيئة الخريطة الرئيسية');
            } catch (error) {
                console.error('❌ خطأ في تهيئة الخريطة الرئيسية:', error);
                // محاولة إعادة تعيين العنصر وإعادة المحاولة
                this.resetMapElement('mobile-map-new');
            }
        }, 1000);
    }

    /**
     * تهيئة خريطة إضافة المتجر
     */
    initAddStoreMap() {
        // انتظار تحميل DOM
        setTimeout(() => {
            const mapElement = document.getElementById('mobile-add-store-map');
            if (!mapElement) {
                console.warn('⚠️ عنصر خريطة إضافة المتجر غير موجود');
                return;
            }

            if (typeof L === 'undefined') {
                console.warn('⚠️ مكتبة Leaflet غير محملة');
                return;
            }

            // التحقق من وجود خريطة مهيئة مسبقاً
            if (this.addStoreMap) {
                console.log('⚠️ خريطة إضافة المتجر مهيئة مسبقاً، سيتم إعادة استخدامها');
                return;
            }

            // التحقق من وجود خريطة في العنصر
            if (mapElement._leaflet_id) {
                console.log('⚠️ عنصر خريطة إضافة المتجر مستخدم مسبقاً، سيتم إعادة تعيينه');
                mapElement._leaflet_id = null;
                mapElement.innerHTML = '';
            }

            try {
                this.addStoreMap = L.map('mobile-add-store-map').setView([32.8872, 13.1913], 6);

                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors'
                }).addTo(this.addStoreMap);

                // إضافة مستمع النقر لتحديد الموقع
                this.addStoreMap.on('click', (e) => {
                    this.setSelectedLocation(e.latlng);
                });

                console.log('✅ تم تهيئة خريطة إضافة المتجر');
            } catch (error) {
                console.error('❌ خطأ في تهيئة خريطة إضافة المتجر:', error);
                // محاولة إعادة تعيين العنصر وإعادة المحاولة
                this.resetMapElement('mobile-add-store-map');
            }
        }, 1200);
    }

    /**
     * إعادة تعيين عنصر الخريطة
     */
    resetMapElement(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            // إزالة جميع البيانات المرتبطة بـ Leaflet
            if (element._leaflet_id) {
                delete element._leaflet_id;
            }

            // مسح محتوى العنصر
            element.innerHTML = '';

            // إزالة جميع الفئات المرتبطة بـ Leaflet
            element.className = element.className.replace(/leaflet-\S+/g, '');

            console.log(`🔄 تم إعادة تعيين عنصر الخريطة: ${elementId}`);
        }
    }

    /**
     * تحديد الموقع المختار
     */
    setSelectedLocation(latlng) {
        this.selectedLocation = latlng;

        // إزالة العلامة السابقة
        if (this.selectedMarker) {
            this.addStoreMap.removeLayer(this.selectedMarker);
        }

        // إضافة علامة جديدة
        this.selectedMarker = L.marker([latlng.lat, latlng.lng]).addTo(this.addStoreMap);

        // تحديث النص
        const locationElement = document.getElementById('mobile-selected-location');
        if (locationElement) {
            locationElement.textContent = `تم تحديد الموقع: ${latlng.lat.toFixed(6)}, ${latlng.lng.toFixed(6)}`;
            locationElement.className = 'mt-2 text-success';
        }

        this.showNotification('تم تحديد الموقع بنجاح', 'success');
    }

    /**
     * تحميل البيانات
     */
    loadData() {
        this.loadStores();
        this.loadStatistics();
        this.loadRecentActivity();
    }

    /**
     * تحميل المتاجر
     */
    async loadStores() {
        try {
            console.log('📡 جاري تحميل المتاجر...');
            
            const response = await fetch('/api/stores');
            const data = await response.json();
            
            if (data && Array.isArray(data)) {
                this.stores = data;
                this.renderStores();
                this.renderStoreMarkers();
                console.log(`✅ تم تحميل ${this.stores.length} متجر`);
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل المتاجر:', error);
            this.showNotification('خطأ في تحميل المتاجر', 'error');
        }
    }

    /**
     * عرض المتاجر في القائمة
     */
    renderStores() {
        const storesList = document.getElementById('mobileStoresList');
        if (!storesList) return;

        if (this.stores.length === 0) {
            storesList.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-store mb-3" style="font-size: 48px; color: var(--text-muted);"></i>
                    <p class="text-muted">لا توجد متاجر حالياً</p>
                </div>
            `;
            return;
        }

        storesList.innerHTML = '';
        
        this.stores.forEach(store => {
            const storeElement = this.createStoreElement(store);
            storesList.appendChild(storeElement);
        });
    }

    /**
     * إنشاء عنصر متجر
     */
    createStoreElement(store) {
        const div = document.createElement('div');
        div.className = 'store-item';
        div.innerHTML = `
            <div class="store-name">${store.name || 'متجر بدون اسم'}</div>
            <div class="store-details">
                <i class="fas fa-map-marker-alt"></i> ${store.city_name || 'غير محدد'} - ${store.region_name || 'غير محدد'}
                <br>
                <i class="fas fa-phone"></i> ${store.phone || 'لا يوجد رقم'}
                <span class="status-indicator status-online"></span>
            </div>
            <div class="store-actions">
                <button class="btn btn-primary-mobile btn-sm-mobile" onclick="mobileApp.viewStore(${store.id})">
                    <i class="fas fa-eye"></i> عرض
                </button>
                <button class="btn btn-secondary-mobile btn-sm-mobile" onclick="mobileApp.editStore(${store.id})">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="btn btn-danger-mobile btn-sm-mobile" onclick="mobileApp.deleteStore(${store.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        return div;
    }

    /**
     * عرض علامات المتاجر على الخريطة
     */
    renderStoreMarkers() {
        if (!this.map) return;

        this.stores.forEach(store => {
            if (store.latitude && store.longitude) {
                const marker = L.marker([store.latitude, store.longitude]).addTo(this.map);
                marker.bindPopup(`
                    <div class="store-popup">
                        <h6>${store.name}</h6>
                        <p><i class="fas fa-map-marker-alt"></i> ${store.city_name} - ${store.region_name}</p>
                        <p><i class="fas fa-phone"></i> ${store.phone}</p>
                        <button class="btn btn-primary-mobile btn-sm-mobile" onclick="mobileApp.viewStore(${store.id})">
                            <i class="fas fa-eye"></i> عرض
                        </button>
                    </div>
                `);
            }
        });
    }

    /**
     * تحميل الإحصائيات
     */
    async loadStatistics() {
        try {
            // محاكاة تحميل الإحصائيات
            this.statistics = {
                totalStores: this.stores.length,
                newStores: Math.floor(this.stores.length * 0.1),
                activityRate: 95,
                pendingStores: Math.floor(this.stores.length * 0.05)
            };

            this.updateStatisticsDisplay();
        } catch (error) {
            console.error('❌ خطأ في تحميل الإحصائيات:', error);
        }
    }

    /**
     * تحديث عرض الإحصائيات
     */
    updateStatisticsDisplay() {
        try {
            // انتظار تحميل DOM
            setTimeout(() => {
                const totalStoresElement = document.getElementById('mobile-total-stores');
                if (totalStoresElement) {
                    totalStoresElement.textContent = this.statistics.totalStores;
                } else {
                    console.warn('⚠️ عنصر إجمالي المتاجر غير موجود');
                }

                const newStoresElement = document.getElementById('mobile-new-stores');
                if (newStoresElement) {
                    newStoresElement.textContent = this.statistics.newStores;
                } else {
                    console.warn('⚠️ عنصر المتاجر الجديدة غير موجود');
                }

                const activityRateElement = document.getElementById('mobile-activity-rate');
                if (activityRateElement) {
                    activityRateElement.textContent = `${this.statistics.activityRate}%`;
                } else {
                    console.warn('⚠️ عنصر معدل النشاط غير موجود');
                }

                const pendingStoresElement = document.getElementById('mobile-pending-stores');
                if (pendingStoresElement) {
                    pendingStoresElement.textContent = this.statistics.pendingStores;
                } else {
                    console.warn('⚠️ عنصر المتاجر المعلقة غير موجود');
                }

                console.log('📊 تم تحديث الإحصائيات');
            }, 500);
        } catch (error) {
            console.error('❌ خطأ في تحديث الإحصائيات:', error);
        }
    }

    /**
     * تحميل النشاط الأخير
     */
    loadRecentActivity() {
        const activityList = document.getElementById('mobile-activity-list');
        if (!activityList) return;

        const activities = [
            {
                icon: 'fas fa-plus text-success',
                title: 'تم إضافة متجر جديد',
                time: 'منذ 5 دقائق'
            },
            {
                icon: 'fas fa-edit text-warning',
                title: 'تم تحديث بيانات متجر',
                time: 'منذ 15 دقيقة'
            },
            {
                icon: 'fas fa-trash text-danger',
                title: 'تم حذف متجر',
                time: 'منذ ساعة'
            }
        ];

        activityList.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-time">${activity.time}</div>
                </div>
            </div>
        `).join('');
    }

    /**
     * إعداد مستمعات الأحداث
     */
    setupEventListeners() {
        // البحث في المتاجر
        const searchInput = document.getElementById('mobileStoreSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterStores(e.target.value);
            });
        }

        // نموذج إضافة متجر
        const addStoreForm = document.getElementById('mobileAddStoreForm');
        if (addStoreForm) {
            addStoreForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.addNewStore();
            });
        }

        // تبديل التبويبات
        const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
        tabButtons.forEach(button => {
            button.addEventListener('shown.bs.tab', (e) => {
                const targetId = e.target.getAttribute('data-bs-target');
                this.handleTabChange(targetId);
            });
        });

        // إعداد قوائم المدن والمناطق
        this.setupCityDistrictSelectors();
    }

    /**
     * إعداد قوائم المدن والمناطق
     */
    setupCityDistrictSelectors() {
        const citySelect = document.querySelector('select[name="storeCity"]');
        const districtSelect = document.querySelector('select[name="storeDistrict"]');

        if (citySelect && districtSelect) {
            citySelect.addEventListener('change', (e) => {
                this.updateDistrictOptions(e.target.value, districtSelect);
            });
        }
    }

    /**
     * تحديث خيارات المناطق بناءً على المدينة
     */
    updateDistrictOptions(city, districtSelect) {
        const districts = {
            'طرابلس': ['وسط المدينة', 'الظهرة', 'أبو سليم', 'الأندلس', 'الدهماني'],
            'مصراتة': ['وسط المدينة', 'الزروق', 'القادسية', 'الصياد'],
            'بنغازي': ['وسط المدينة', 'الصابري', 'الليثي', 'الكيش']
        };

        districtSelect.innerHTML = '<option value="">اختر المنطقة</option>';

        if (districts[city]) {
            districts[city].forEach(district => {
                const option = document.createElement('option');
                option.value = district;
                option.textContent = district;
                districtSelect.appendChild(option);
            });
        }
    }

    /**
     * معالجة تغيير التبويبات
     */
    handleTabChange(targetId) {
        if (targetId === '#map-view') {
            setTimeout(() => {
                if (this.map) {
                    try {
                        this.map.invalidateSize();
                        console.log('🗺️ تم تحديث حجم الخريطة الرئيسية');
                    } catch (error) {
                        console.warn('⚠️ خطأ في تحديث الخريطة الرئيسية:', error);
                        // إعادة تهيئة الخريطة في حالة الخطأ
                        this.initMainMap();
                    }
                } else {
                    // تهيئة الخريطة إذا لم تكن موجودة
                    this.initMainMap();
                }
            }, 200);
        }

        if (targetId === '#add-store') {
            setTimeout(() => {
                if (this.addStoreMap) {
                    try {
                        this.addStoreMap.invalidateSize();
                        console.log('🗺️ تم تحديث حجم خريطة إضافة المتجر');
                    } catch (error) {
                        console.warn('⚠️ خطأ في تحديث خريطة إضافة المتجر:', error);
                        // إعادة تهيئة الخريطة في حالة الخطأ
                        this.initAddStoreMap();
                    }
                } else {
                    // تهيئة الخريطة إذا لم تكن موجودة
                    this.initAddStoreMap();
                }
            }, 200);
        }
    }

    /**
     * إضافة الأنيميشن
     */
    addAnimations() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                }
            });
        });

        document.querySelectorAll('.mobile-card, .store-item').forEach(el => {
            observer.observe(el);
        });
    }

    /**
     * تبديل التبويبات
     */
    switchTab(tabId) {
        const tab = document.getElementById(tabId);
        if (tab) {
            const tabInstance = new bootstrap.Tab(tab);
            tabInstance.show();
        }
    }

    /**
     * عرض إشعار
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }

    /**
     * تصفية المتاجر
     */
    filterStores(searchTerm) {
        const storeItems = document.querySelectorAll('.store-item');
        storeItems.forEach(item => {
            const storeName = item.querySelector('.store-name').textContent;
            const storeDetails = item.querySelector('.store-details').textContent;

            if (storeName.includes(searchTerm) || storeDetails.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }

    /**
     * إضافة متجر جديد
     */
    async addNewStore() {
        if (!this.selectedLocation) {
            this.showNotification('يرجى تحديد موقع المتجر على الخريطة', 'warning');
            return;
        }

        const form = document.getElementById('mobileAddStoreForm');
        const formData = new FormData(form);

        const storeData = {
            name: formData.get('storeName'),
            phone: formData.get('storePhone'),
            city: formData.get('storeCity'),
            district: formData.get('storeDistrict'),
            address: formData.get('storeAddress'),
            description: formData.get('storeDescription'),
            type: formData.get('storeType'),
            latitude: this.selectedLocation.lat,
            longitude: this.selectedLocation.lng
        };

        try {
            this.showNotification('جاري إضافة المتجر...', 'info');

            const response = await fetch('/api/stores', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(storeData)
            });

            if (response.ok) {
                this.showNotification('تم إضافة المتجر بنجاح', 'success');
                form.reset();
                this.clearSelectedLocation();
                this.loadStores(); // إعادة تحميل المتاجر
                this.loadStatistics(); // تحديث الإحصائيات
            } else {
                throw new Error('فشل في إضافة المتجر');
            }
        } catch (error) {
            console.error('❌ خطأ في إضافة المتجر:', error);
            this.showNotification('خطأ في إضافة المتجر', 'error');
        }
    }

    /**
     * مسح الموقع المحدد
     */
    clearSelectedLocation() {
        if (this.selectedMarker) {
            this.addStoreMap.removeLayer(this.selectedMarker);
            this.selectedMarker = null;
        }
        this.selectedLocation = null;

        const locationElement = document.getElementById('mobile-selected-location');
        if (locationElement) {
            locationElement.textContent = 'لم يتم تحديد موقع';
            locationElement.className = 'mt-2 text-info';
        }
    }

    /**
     * عرض متجر
     */
    viewStore(storeId) {
        this.showNotification(`عرض تفاصيل المتجر رقم ${storeId}`, 'info');
        // يمكن إضافة منطق عرض تفاصيل المتجر هنا
    }

    /**
     * تعديل متجر
     */
    editStore(storeId) {
        this.showNotification(`تعديل المتجر رقم ${storeId}`, 'info');
        // يمكن إضافة منطق تعديل المتجر هنا
    }

    /**
     * حذف متجر
     */
    async deleteStore(storeId) {
        if (!confirm('هل أنت متأكد من حذف هذا المتجر؟')) {
            return;
        }

        try {
            const response = await fetch(`/api/stores/${storeId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                this.showNotification('تم حذف المتجر بنجاح', 'success');
                this.loadStores(); // إعادة تحميل المتاجر
                this.loadStatistics(); // تحديث الإحصائيات
            } else {
                throw new Error('فشل في حذف المتجر');
            }
        } catch (error) {
            console.error('❌ خطأ في حذف المتجر:', error);
            this.showNotification('خطأ في حذف المتجر', 'error');
        }
    }

    /**
     * تحديث المتاجر
     */
    refreshStores() {
        this.showNotification('جاري تحديث قائمة المتاجر...', 'info');
        this.loadStores();
    }

    /**
     * تحميل المزيد من المتاجر
     */
    loadMoreStores() {
        this.showNotification('جاري تحميل المزيد من المتاجر...', 'info');
        // يمكن إضافة منطق تحميل المزيد هنا
    }

    /**
     * الحصول على الموقع الحالي
     */
    getCurrentLocation() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    this.currentLocation = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };

                    if (this.map) {
                        this.map.setView([this.currentLocation.lat, this.currentLocation.lng], 15);
                        L.marker([this.currentLocation.lat, this.currentLocation.lng])
                            .addTo(this.map)
                            .bindPopup('موقعك الحالي')
                            .openPopup();
                    }

                    this.showNotification('تم تحديد موقعك الحالي', 'success');
                },
                (error) => {
                    this.showNotification('لا يمكن تحديد موقعك الحالي', 'error');
                }
            );
        } else {
            this.showNotification('المتصفح لا يدعم تحديد الموقع', 'error');
        }
    }

    /**
     * البحث عن موقع
     */
    searchLocation() {
        this.showNotification('ميزة البحث عن الموقع قيد التطوير', 'info');
    }

    /**
     * تصفية المتاجر
     */
    filterStores() {
        this.showNotification('ميزة تصفية المتاجر قيد التطوير', 'info');
    }

    /**
     * تصدير البيانات
     */
    exportData() {
        this.showNotification('جاري تصدير البيانات...', 'info');
        setTimeout(() => {
            this.showNotification('تم تصدير البيانات بنجاح', 'success');
        }, 1000);
    }

    /**
     * عرض الإعدادات
     */
    showSettings() {
        this.showNotification('فتح الإعدادات...', 'info');
    }

    /**
     * إعادة تعيين التطبيق
     */
    resetApp() {
        console.log('🔄 إعادة تعيين التطبيق المحمول...');

        try {
            // تنظيف الخرائط
            this.cleanupExistingMaps();

            // إعادة تعيين المتغيرات
            this.stores = [];
            this.currentLocation = null;
            this.selectedLocation = null;
            this.selectedMarker = null;

            // إعادة تهيئة التطبيق
            setTimeout(() => {
                this.init();
            }, 500);

            console.log('✅ تم إعادة تعيين التطبيق بنجاح');
        } catch (error) {
            console.error('❌ خطأ في إعادة تعيين التطبيق:', error);
        }
    }

    /**
     * إصلاح مشاكل الخرائط
     */
    fixMapIssues() {
        console.log('🔧 إصلاح مشاكل الخرائط...');

        // تنظيف وإعادة تهيئة الخرائط
        this.cleanupExistingMaps();

        setTimeout(() => {
            this.initializeMaps();
        }, 300);
    }
}

// إضافة دوال عامة لإصلاح المشاكل
window.fixMobileMapIssues = function() {
    if (window.mobileApp) {
        window.mobileApp.fixMapIssues();
    } else {
        console.log('⚠️ التطبيق المحمول غير مهيئ');
    }
};

window.resetMobileApp = function() {
    if (window.mobileApp) {
        window.mobileApp.resetApp();
    } else {
        console.log('⚠️ التطبيق المحمول غير مهيئ');
    }
};

// إضافة معالج للأخطاء العامة
window.addEventListener('error', function(event) {
    if (event.error && event.error.message && event.error.message.includes('Map container is already initialized')) {
        console.log('🔧 تم اكتشاف خطأ في الخريطة، سيتم الإصلاح تلقائياً...');
        setTimeout(() => {
            if (window.mobileApp) {
                window.mobileApp.fixMapIssues();
            }
        }, 1000);
    }
});
