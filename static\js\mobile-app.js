/**
 * MobileApp - فئة التطبيق المحمول الجديد
 * تدير جميع وظائف الواجهة المحمولة الجديدة
 */
class MobileApp {
    constructor() {
        this.map = null;
        this.addStoreMap = null;
        this.stores = [];
        this.currentLocation = null;
        this.selectedLocation = null;
        this.selectedMarker = null;
        this.currentUser = null;
        this.statistics = {
            totalStores: 0,
            newStores: 0,
            activityRate: 0,
            pendingStores: 0
        };

        this.init();
    }

    /**
     * تهيئة التطبيق
     */
    init() {
        console.log('🚀 تهيئة التطبيق المحمول...');
        
        // تحميل بيانات المستخدم
        this.loadUserData();
        
        // تهيئة الخرائط
        setTimeout(() => {
            this.initializeMaps();
        }, 500);
        
        // تحميل البيانات
        this.loadData();
        
        // إعداد مستمعات الأحداث
        this.setupEventListeners();
        
        // إضافة الأنيميشن
        this.addAnimations();
        
        console.log('✅ تم تهيئة التطبيق المحمول بنجاح');
    }

    /**
     * تحميل بيانات المستخدم
     */
    loadUserData() {
        // محاولة الحصول على بيانات المستخدم من العناصر الموجودة
        const userNameElement = document.querySelector('.user-info h6');
        const userRoleElement = document.querySelector('.user-info small');
        
        if (userNameElement && userNameElement.textContent.includes('مرحباً')) {
            const userName = userNameElement.textContent.replace('مرحباً، ', '');
            document.getElementById('mobile-user-name').textContent = `مرحباً، ${userName}`;
        }
        
        if (userRoleElement) {
            document.getElementById('mobile-user-role').textContent = userRoleElement.textContent;
        }

        // تحديث بيانات المستخدم من الخادم
        this.fetchUserData();
    }

    /**
     * جلب بيانات المستخدم من الخادم
     */
    async fetchUserData() {
        try {
            // يمكن إضافة API endpoint هنا لجلب بيانات المستخدم
            console.log('📡 جاري تحميل بيانات المستخدم...');
        } catch (error) {
            console.error('❌ خطأ في تحميل بيانات المستخدم:', error);
        }
    }

    /**
     * تهيئة الخرائط
     */
    initializeMaps() {
        console.log('🗺️ تهيئة الخرائط...');
        
        // تهيئة الخريطة الرئيسية
        this.initMainMap();
        
        // تهيئة خريطة إضافة المتجر
        this.initAddStoreMap();
    }

    /**
     * تهيئة الخريطة الرئيسية
     */
    initMainMap() {
        const mapElement = document.getElementById('mobile-map-new');
        if (!mapElement || typeof L === 'undefined') {
            console.warn('⚠️ عنصر الخريطة غير موجود أو مكتبة Leaflet غير محملة');
            return;
        }

        try {
            this.map = L.map('mobile-map-new').setView([32.8872, 13.1913], 6);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(this.map);

            console.log('✅ تم تهيئة الخريطة الرئيسية');
        } catch (error) {
            console.error('❌ خطأ في تهيئة الخريطة الرئيسية:', error);
        }
    }

    /**
     * تهيئة خريطة إضافة المتجر
     */
    initAddStoreMap() {
        const mapElement = document.getElementById('mobile-add-store-map');
        if (!mapElement || typeof L === 'undefined') {
            return;
        }

        try {
            this.addStoreMap = L.map('mobile-add-store-map').setView([32.8872, 13.1913], 6);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(this.addStoreMap);

            // إضافة مستمع النقر لتحديد الموقع
            this.addStoreMap.on('click', (e) => {
                this.setSelectedLocation(e.latlng);
            });

            console.log('✅ تم تهيئة خريطة إضافة المتجر');
        } catch (error) {
            console.error('❌ خطأ في تهيئة خريطة إضافة المتجر:', error);
        }
    }

    /**
     * تحديد الموقع المختار
     */
    setSelectedLocation(latlng) {
        this.selectedLocation = latlng;

        // إزالة العلامة السابقة
        if (this.selectedMarker) {
            this.addStoreMap.removeLayer(this.selectedMarker);
        }

        // إضافة علامة جديدة
        this.selectedMarker = L.marker([latlng.lat, latlng.lng]).addTo(this.addStoreMap);

        // تحديث النص
        const locationElement = document.getElementById('mobile-selected-location');
        if (locationElement) {
            locationElement.textContent = `تم تحديد الموقع: ${latlng.lat.toFixed(6)}, ${latlng.lng.toFixed(6)}`;
            locationElement.className = 'mt-2 text-success';
        }

        this.showNotification('تم تحديد الموقع بنجاح', 'success');
    }

    /**
     * تحميل البيانات
     */
    loadData() {
        this.loadStores();
        this.loadStatistics();
        this.loadRecentActivity();
    }

    /**
     * تحميل المتاجر
     */
    async loadStores() {
        try {
            console.log('📡 جاري تحميل المتاجر...');
            
            const response = await fetch('/api/stores');
            const data = await response.json();
            
            if (data && Array.isArray(data)) {
                this.stores = data;
                this.renderStores();
                this.renderStoreMarkers();
                console.log(`✅ تم تحميل ${this.stores.length} متجر`);
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل المتاجر:', error);
            this.showNotification('خطأ في تحميل المتاجر', 'error');
        }
    }

    /**
     * عرض المتاجر في القائمة
     */
    renderStores() {
        const storesList = document.getElementById('mobileStoresList');
        if (!storesList) return;

        if (this.stores.length === 0) {
            storesList.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-store mb-3" style="font-size: 48px; color: var(--text-muted);"></i>
                    <p class="text-muted">لا توجد متاجر حالياً</p>
                </div>
            `;
            return;
        }

        storesList.innerHTML = '';
        
        this.stores.forEach(store => {
            const storeElement = this.createStoreElement(store);
            storesList.appendChild(storeElement);
        });
    }

    /**
     * إنشاء عنصر متجر
     */
    createStoreElement(store) {
        const div = document.createElement('div');
        div.className = 'store-item';
        div.innerHTML = `
            <div class="store-name">${store.name || 'متجر بدون اسم'}</div>
            <div class="store-details">
                <i class="fas fa-map-marker-alt"></i> ${store.city_name || 'غير محدد'} - ${store.region_name || 'غير محدد'}
                <br>
                <i class="fas fa-phone"></i> ${store.phone || 'لا يوجد رقم'}
                <span class="status-indicator status-online"></span>
            </div>
            <div class="store-actions">
                <button class="btn btn-primary-mobile btn-sm-mobile" onclick="mobileApp.viewStore(${store.id})">
                    <i class="fas fa-eye"></i> عرض
                </button>
                <button class="btn btn-secondary-mobile btn-sm-mobile" onclick="mobileApp.editStore(${store.id})">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="btn btn-danger-mobile btn-sm-mobile" onclick="mobileApp.deleteStore(${store.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        return div;
    }

    /**
     * عرض علامات المتاجر على الخريطة
     */
    renderStoreMarkers() {
        if (!this.map) return;

        this.stores.forEach(store => {
            if (store.latitude && store.longitude) {
                const marker = L.marker([store.latitude, store.longitude]).addTo(this.map);
                marker.bindPopup(`
                    <div class="store-popup">
                        <h6>${store.name}</h6>
                        <p><i class="fas fa-map-marker-alt"></i> ${store.city_name} - ${store.region_name}</p>
                        <p><i class="fas fa-phone"></i> ${store.phone}</p>
                        <button class="btn btn-primary-mobile btn-sm-mobile" onclick="mobileApp.viewStore(${store.id})">
                            <i class="fas fa-eye"></i> عرض
                        </button>
                    </div>
                `);
            }
        });
    }

    /**
     * تحميل الإحصائيات
     */
    async loadStatistics() {
        try {
            // محاكاة تحميل الإحصائيات
            this.statistics = {
                totalStores: this.stores.length,
                newStores: Math.floor(this.stores.length * 0.1),
                activityRate: 95,
                pendingStores: Math.floor(this.stores.length * 0.05)
            };

            this.updateStatisticsDisplay();
        } catch (error) {
            console.error('❌ خطأ في تحميل الإحصائيات:', error);
        }
    }

    /**
     * تحديث عرض الإحصائيات
     */
    updateStatisticsDisplay() {
        document.getElementById('mobile-total-stores').textContent = this.statistics.totalStores;
        document.getElementById('mobile-new-stores').textContent = this.statistics.newStores;
        document.getElementById('mobile-activity-rate').textContent = `${this.statistics.activityRate}%`;
        document.getElementById('mobile-pending-stores').textContent = this.statistics.pendingStores;
    }

    /**
     * تحميل النشاط الأخير
     */
    loadRecentActivity() {
        const activityList = document.getElementById('mobile-activity-list');
        if (!activityList) return;

        const activities = [
            {
                icon: 'fas fa-plus text-success',
                title: 'تم إضافة متجر جديد',
                time: 'منذ 5 دقائق'
            },
            {
                icon: 'fas fa-edit text-warning',
                title: 'تم تحديث بيانات متجر',
                time: 'منذ 15 دقيقة'
            },
            {
                icon: 'fas fa-trash text-danger',
                title: 'تم حذف متجر',
                time: 'منذ ساعة'
            }
        ];

        activityList.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-time">${activity.time}</div>
                </div>
            </div>
        `).join('');
    }

    /**
     * إعداد مستمعات الأحداث
     */
    setupEventListeners() {
        // البحث في المتاجر
        const searchInput = document.getElementById('mobileStoreSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterStores(e.target.value);
            });
        }

        // نموذج إضافة متجر
        const addStoreForm = document.getElementById('mobileAddStoreForm');
        if (addStoreForm) {
            addStoreForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.addNewStore();
            });
        }

        // تبديل التبويبات
        const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
        tabButtons.forEach(button => {
            button.addEventListener('shown.bs.tab', (e) => {
                const targetId = e.target.getAttribute('data-bs-target');
                this.handleTabChange(targetId);
            });
        });

        // إعداد قوائم المدن والمناطق
        this.setupCityDistrictSelectors();
    }

    /**
     * إعداد قوائم المدن والمناطق
     */
    setupCityDistrictSelectors() {
        const citySelect = document.querySelector('select[name="storeCity"]');
        const districtSelect = document.querySelector('select[name="storeDistrict"]');

        if (citySelect && districtSelect) {
            citySelect.addEventListener('change', (e) => {
                this.updateDistrictOptions(e.target.value, districtSelect);
            });
        }
    }

    /**
     * تحديث خيارات المناطق بناءً على المدينة
     */
    updateDistrictOptions(city, districtSelect) {
        const districts = {
            'طرابلس': ['وسط المدينة', 'الظهرة', 'أبو سليم', 'الأندلس', 'الدهماني'],
            'مصراتة': ['وسط المدينة', 'الزروق', 'القادسية', 'الصياد'],
            'بنغازي': ['وسط المدينة', 'الصابري', 'الليثي', 'الكيش']
        };

        districtSelect.innerHTML = '<option value="">اختر المنطقة</option>';

        if (districts[city]) {
            districts[city].forEach(district => {
                const option = document.createElement('option');
                option.value = district;
                option.textContent = district;
                districtSelect.appendChild(option);
            });
        }
    }

    /**
     * معالجة تغيير التبويبات
     */
    handleTabChange(targetId) {
        if (targetId === '#map-view' && this.map) {
            setTimeout(() => this.map.invalidateSize(), 100);
        }
        if (targetId === '#add-store' && this.addStoreMap) {
            setTimeout(() => this.addStoreMap.invalidateSize(), 100);
        }
    }

    /**
     * إضافة الأنيميشن
     */
    addAnimations() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                }
            });
        });

        document.querySelectorAll('.mobile-card, .store-item').forEach(el => {
            observer.observe(el);
        });
    }

    /**
     * تبديل التبويبات
     */
    switchTab(tabId) {
        const tab = document.getElementById(tabId);
        if (tab) {
            const tabInstance = new bootstrap.Tab(tab);
            tabInstance.show();
        }
    }

    /**
     * عرض إشعار
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }

    /**
     * تصفية المتاجر
     */
    filterStores(searchTerm) {
        const storeItems = document.querySelectorAll('.store-item');
        storeItems.forEach(item => {
            const storeName = item.querySelector('.store-name').textContent;
            const storeDetails = item.querySelector('.store-details').textContent;

            if (storeName.includes(searchTerm) || storeDetails.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }

    /**
     * إضافة متجر جديد
     */
    async addNewStore() {
        if (!this.selectedLocation) {
            this.showNotification('يرجى تحديد موقع المتجر على الخريطة', 'warning');
            return;
        }

        const form = document.getElementById('mobileAddStoreForm');
        const formData = new FormData(form);

        const storeData = {
            name: formData.get('storeName'),
            phone: formData.get('storePhone'),
            city: formData.get('storeCity'),
            district: formData.get('storeDistrict'),
            address: formData.get('storeAddress'),
            description: formData.get('storeDescription'),
            type: formData.get('storeType'),
            latitude: this.selectedLocation.lat,
            longitude: this.selectedLocation.lng
        };

        try {
            this.showNotification('جاري إضافة المتجر...', 'info');

            const response = await fetch('/api/stores', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(storeData)
            });

            if (response.ok) {
                this.showNotification('تم إضافة المتجر بنجاح', 'success');
                form.reset();
                this.clearSelectedLocation();
                this.loadStores(); // إعادة تحميل المتاجر
                this.loadStatistics(); // تحديث الإحصائيات
            } else {
                throw new Error('فشل في إضافة المتجر');
            }
        } catch (error) {
            console.error('❌ خطأ في إضافة المتجر:', error);
            this.showNotification('خطأ في إضافة المتجر', 'error');
        }
    }

    /**
     * مسح الموقع المحدد
     */
    clearSelectedLocation() {
        if (this.selectedMarker) {
            this.addStoreMap.removeLayer(this.selectedMarker);
            this.selectedMarker = null;
        }
        this.selectedLocation = null;

        const locationElement = document.getElementById('mobile-selected-location');
        if (locationElement) {
            locationElement.textContent = 'لم يتم تحديد موقع';
            locationElement.className = 'mt-2 text-info';
        }
    }

    /**
     * عرض متجر
     */
    viewStore(storeId) {
        this.showNotification(`عرض تفاصيل المتجر رقم ${storeId}`, 'info');
        // يمكن إضافة منطق عرض تفاصيل المتجر هنا
    }

    /**
     * تعديل متجر
     */
    editStore(storeId) {
        this.showNotification(`تعديل المتجر رقم ${storeId}`, 'info');
        // يمكن إضافة منطق تعديل المتجر هنا
    }

    /**
     * حذف متجر
     */
    async deleteStore(storeId) {
        if (!confirm('هل أنت متأكد من حذف هذا المتجر؟')) {
            return;
        }

        try {
            const response = await fetch(`/api/stores/${storeId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                this.showNotification('تم حذف المتجر بنجاح', 'success');
                this.loadStores(); // إعادة تحميل المتاجر
                this.loadStatistics(); // تحديث الإحصائيات
            } else {
                throw new Error('فشل في حذف المتجر');
            }
        } catch (error) {
            console.error('❌ خطأ في حذف المتجر:', error);
            this.showNotification('خطأ في حذف المتجر', 'error');
        }
    }

    /**
     * تحديث المتاجر
     */
    refreshStores() {
        this.showNotification('جاري تحديث قائمة المتاجر...', 'info');
        this.loadStores();
    }

    /**
     * تحميل المزيد من المتاجر
     */
    loadMoreStores() {
        this.showNotification('جاري تحميل المزيد من المتاجر...', 'info');
        // يمكن إضافة منطق تحميل المزيد هنا
    }

    /**
     * الحصول على الموقع الحالي
     */
    getCurrentLocation() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    this.currentLocation = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };

                    if (this.map) {
                        this.map.setView([this.currentLocation.lat, this.currentLocation.lng], 15);
                        L.marker([this.currentLocation.lat, this.currentLocation.lng])
                            .addTo(this.map)
                            .bindPopup('موقعك الحالي')
                            .openPopup();
                    }

                    this.showNotification('تم تحديد موقعك الحالي', 'success');
                },
                (error) => {
                    this.showNotification('لا يمكن تحديد موقعك الحالي', 'error');
                }
            );
        } else {
            this.showNotification('المتصفح لا يدعم تحديد الموقع', 'error');
        }
    }

    /**
     * البحث عن موقع
     */
    searchLocation() {
        this.showNotification('ميزة البحث عن الموقع قيد التطوير', 'info');
    }

    /**
     * تصفية المتاجر
     */
    filterStores() {
        this.showNotification('ميزة تصفية المتاجر قيد التطوير', 'info');
    }

    /**
     * تصدير البيانات
     */
    exportData() {
        this.showNotification('جاري تصدير البيانات...', 'info');
        setTimeout(() => {
            this.showNotification('تم تصدير البيانات بنجاح', 'success');
        }, 1000);
    }

    /**
     * عرض الإعدادات
     */
    showSettings() {
        this.showNotification('فتح الإعدادات...', 'info');
    }
}
